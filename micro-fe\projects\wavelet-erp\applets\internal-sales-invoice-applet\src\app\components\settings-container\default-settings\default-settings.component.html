<div style="text-align: right; margin-top: 16px;">
  <button mat-raised-button color="primary" (click)="onSave()">SAVE</button>
</div>
<div style="margin: 10px;">
  <mat-accordion>
    <mat-expansion-panel expanded="true">
      <mat-expansion-panel-header>
        <mat-panel-title>
          <span class="field-header">Applet Default Settings</span>
        </mat-panel-title>
      </mat-expansion-panel-header>
      <ul>
        <li>
          Default Branch: <blg-select-branch-drop-down-v2 [apiVisa]="apiVisa" [(branch)]="form.controls['DEFAULT_BRANCH']" (branchSelected)="onBranchSelected($event)" [branchGuids]="branchGuids"></blg-select-branch-drop-down-v2>
        </li>
        <li>
          Default Location: <blg-select-location-drop-down-v2 [branchSelectedGuid]="selectedBranch" [apiVisa]="apiVisa" [(location)]="form.controls['DEFAULT_LOCATION']"></blg-select-location-drop-down-v2>
        </li>
        <li>
          Default Pricing Scheme: <app-pricing-scheme-v2 [(pricingScheme)]="form.controls['PRICING_RETAIL_GUID']"></app-pricing-scheme-v2>
        </li>
        <li>
          Default Pricebook: <app-util-pricebook [(priceBook)]="form.controls['DEFAULT_PRICEBOOK']"></app-util-pricebook>
         </li>
         <li>
          Floating Point Precision:
          <mat-form-field class="" appearance="outline">
            <mat-label>Unit Price Decimal Precision</mat-label>
            <input
              matInput
              [formControl]="form.controls['DEFAULT_DECIMAL_PRECISION']"
              autocomplete="off"
              type="number"
              min="2"
              max="4"
              step="1"
              placeholder="Enter decimal places"
            />
            <mat-error>
              Decimal precision must be between <strong>2 and 4</strong>
            </mat-error>
          </mat-form-field>
        </li>
      </ul>
    </mat-expansion-panel>
    <mat-expansion-panel>
      <mat-expansion-panel-header>
        <mat-panel-title>
          <span class="field-header">Details Tab Ordering1</span>
        </mat-panel-title>
      </mat-expansion-panel-header>
      <div cdkDropList class="example-list" (cdkDropListDropped)="drop($event)">
        <div class="example-box" *ngFor="let detailsTab of detailsTabs" cdkDrag>{{ detailsTab.title }}</div>
      </div>
    </mat-expansion-panel>
  </mat-accordion>
</div>
<div style="display: flex; justify-content: center; align-items: center">
  <button mat-raised-button color="warn" (click)="onReset()">RESET</button>
</div>
