import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { StoreModule } from '@ngrx/store';
import { EffectsModule } from '@ngrx/effects';

import { AgGridModule } from 'ag-grid-angular';
import { StockAvailabilityModule } from '../stock-availability-container/stock-availability.module';

import { StockAvailabilityDetailsContainerComponent } from './stock-availability-details-container.component';
import { StockAvailabilityDetailsListingComponent } from './stock-availability-details-listing/stock-availability-details-listing.component';
import { StockAvailabilityDetailsFeatureKey } from '../../state-controllers/stock-availability-details-controller/store/reducers/stock-availability-details.reducers';
import { reducers } from '../../state-controllers/stock-availability-details-controller/store/reducers';
import { StockAvailabilityDetailsEffects } from '../../state-controllers/stock-availability-details-controller/store/effects';
import { StockAvailabilityUtilitiesModule } from '../utilities/utilities.module';
import { UtilitiesModule } from 'projects/shared-utilities/utilities/utilities.module';
import { StockAvailabilityDetailsViewComponent } from './stock-availability-details-view/stock-availability-details-view.component';
import { StockAvailabilityDetailsViewMainComponent } from './stock-availability-details-view/stock-availability-details-view-main/stock-availability-details-view-main.component';
import { StockAvailabilityDetailsViewMovementComponent } from './stock-availability-details-view/stock-availability-details-view-movement/stock-availability-details-view-movement.component';
import { StockAvailabilityDetailsViewAgingComponent } from './stock-availability-details-view/stock-availability-details-view-aging/stock-availability-details-view-aging.component';
import { StockAvailabilityDetailsViewSOComponent } from './stock-availability-details-view/stock-availability-details-view-so/stock-availability-details-view-so.component';
import { StockAvailabilityDetailsViewPOComponent } from './stock-availability-details-view/stock-availability-details-view-po/stock-availability-details-view-po.component';
import { StockAvailabilityDetailsViewGRNComponent } from './stock-availability-details-view/stock-availability-details-view-grn/stock-availability-details-view-grn.component';
import { StockAvailabilityDetailsViewImagesComponent } from './stock-availability-details-view/stock-availability-details-view-images/stock-availability-details-view-images.component';
import { MatDialogModule } from '@angular/material/dialog';
import { GenericDocumentViewComponent } from './stock-availability-details-view/generic-document-view/generic-document-view.component';
import { GenericDocumentViewMainComponent } from './stock-availability-details-view/generic-document-view/generic-document-view-main/generic-document-view-main.component';
import { GenericDocumentViewLineComponent } from './stock-availability-details-view/generic-document-view/generic-document-view-line/generic-document-view-line.component';
import { StockMovementViewComponent } from './stock-availability-details-view/stock-movement-view/stock-movement-view.component';
import { StockAvailabilityDetailsViewSalesReportComponent } from './stock-availability-details-view/stock-availability-details-view-sales-report/stock-availability-details-view-sales-report.component';

@NgModule({
  imports: [
    CommonModule,
    UtilitiesModule,
    StockAvailabilityUtilitiesModule,
    AgGridModule,
    StockAvailabilityModule,
    StoreModule.forFeature(StockAvailabilityDetailsFeatureKey, reducers.StockAvailabilityDetails),
    EffectsModule.forFeature([StockAvailabilityDetailsEffects]),
    MatDialogModule
  ],
  declarations: [
    StockAvailabilityDetailsContainerComponent,
    StockAvailabilityDetailsListingComponent,
    StockAvailabilityDetailsViewComponent,
    StockAvailabilityDetailsViewMainComponent,
    StockAvailabilityDetailsViewMovementComponent,
    StockAvailabilityDetailsViewAgingComponent,
    StockAvailabilityDetailsViewSOComponent,
    StockAvailabilityDetailsViewPOComponent,
    StockAvailabilityDetailsViewGRNComponent,
    StockAvailabilityDetailsViewImagesComponent,
    GenericDocumentViewComponent,
    GenericDocumentViewMainComponent,
    GenericDocumentViewLineComponent,
    StockMovementViewComponent,
    StockAvailabilityDetailsViewSalesReportComponent
  ],
})
export class StockAvailabilityDetailsModule { }
