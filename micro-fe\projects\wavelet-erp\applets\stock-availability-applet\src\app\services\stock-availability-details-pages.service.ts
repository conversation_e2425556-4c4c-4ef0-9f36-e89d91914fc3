import { Injectable } from '@angular/core';
import { StockAvailabilityDetailsListingComponent } from '../components/stock-availability-details-container/stock-availability-details-listing/stock-availability-details-listing.component';
import { ViewColumnState } from 'projects/shared-utilities/application-controller/store/states/view-col.states';
import { ViewColumn } from 'projects/shared-utilities/view-column';
import { StockAvailabilityDetailsViewComponent } from '../components/stock-availability-details-container/stock-availability-details-view/stock-availability-details-view.component';
import { GenericDocumentViewComponent } from '../components/stock-availability-details-container/stock-availability-details-view/generic-document-view/generic-document-view.component'
import { StockMovementViewComponent } from '../components/stock-availability-details-container/stock-availability-details-view/stock-movement-view/stock-movement-view.component';
import { StockAvailabilityDetailsViewSalesReportComponent } from '../components/stock-availability-details-container/stock-availability-details-view/stock-availability-details-view-sales-report/stock-availability-details-view-sales-report.component';

@Injectable({
  providedIn: 'root'
})
export class StockAvailabilityDetailsService {

  private initialState: ViewColumnState = {
    firstColumn:  new ViewColumn(0, StockAvailabilityDetailsListingComponent, 'Stock Availability Details Listing', {
      deactivateAdd: false,
      deactivateList: false
    }),
    secondColumn: null,
    viewCol: [
      new ViewColumn(0, StockAvailabilityDetailsListingComponent, 'Stock Availability Details Listing', {
        deactivateAdd: false,
        deactivateList: false
      }),
      new ViewColumn(1, StockAvailabilityDetailsViewComponent, 'Stock Availability Details View', {
        deactivateAdd: false,
        deactivateList: false
      }),
      new ViewColumn(2, GenericDocumentViewComponent, 'Generic Document View', {
        deactivateReturn: false
      }),
      new ViewColumn(3, StockMovementViewComponent, 'Stock Movement View', {
        deactivateReturn: false
      }),
      new ViewColumn(4, StockAvailabilityDetailsViewSalesReportComponent, 'Sales Report View', {
        deactivateReturn: false
      }),
    ],
    breadCrumbs: [],
    leftDrawer: [],
    rightDrawer: [],
    singleColumn: false,
    prevIndex: null
  };

  get pages() {
    return this.initialState;
  }

  constructor() { }
}
