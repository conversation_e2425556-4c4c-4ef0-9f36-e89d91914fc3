// Angular core
import { Compo<PERSON>, On<PERSON><PERSON>roy, OnInit } from '@angular/core';

// NgRx
import { ComponentStore } from '@ngrx/component-store';
import { Store } from '@ngrx/store';

// 3rd-party libraries
import { GridOptions } from 'ag-grid-enterprise';
import { ToastrService } from 'ngx-toastr';
import { SubSink } from 'subsink2';
import { from, Observable, Subject } from 'rxjs';
import { debounceTime, map, mergeMap, take, toArray } from 'rxjs/operators';

// Shared utilities (external project modules)
import { ListingInputModel } from 'projects/shared-utilities/models/listing-input.model';
import { SearchQueryModel } from 'projects/shared-utilities/models/query.model';
import { ListingService } from 'projects/shared-utilities/services/listing-service';
import { SessionSelectors } from 'projects/shared-utilities/modules/session/session-controller/selectors';
import { SessionStates } from 'projects/shared-utilities/modules/session/session-controller/states';
import { ClientSidePermissionsSelectors } from 'projects/shared-utilities/modules/permission/client-side-permissions-controller/selectors';
import { PermissionStates } from 'projects/shared-utilities/modules/permission/permission-controller';
import { UserPermInquirySelectors } from 'projects/shared-utilities/modules/permission/user-permissions-inquiry-controller/selectors';
import { UtilitiesModule } from 'projects/shared-utilities/utilities/utilities.module';
import { ViewColumnComponent } from 'projects/shared-utilities/view-column.component';
import { AppConfig } from 'projects/shared-utilities/visa';

// Feature-specific (local project modules)
import { ViewColumnFacade } from '../../../facades/view-column.facade';
import { StockAvailabilityDetailsSearchModel } from '../../../models/advanced-search-models/stock-availability-details-search.model';
import { StockAvailabilityInputModel, StockAvailabilityModel } from '../../../models/stock-availability-model';
import { ApiService } from '../../../services/api-service';
import { StockAvailabilityDetailsStates } from '../../../state-controllers/stock-availability-details-controller/store/states';

// Local components
import { CustomTooltip } from '../../utilities/tooltip/custom-tooltip.component';
import { ImageCellRendererLabel } from '../../utilities/cell-renderer/image-cell-renderer.component';
import { MultilineCellRendererComponent } from '../../utilities/cell-renderer/multiline-cell-renderer.component';
import { TotalBalanceCellRendererComponent } from '../../utilities/cell-renderer/total-balance-cell-renderer.component';
import { PricingSchemeTooltip } from '../../utilities/tooltip/pricing-scheme-tooltip.component';

interface LocalState {
  deactivateAdd: boolean;
  deactivateList: boolean;
}

@Component({
  selector: 'app-stock-availability-details-listing',
  templateUrl: './stock-availability-details-listing.component.html',
  styleUrls: ['./stock-availability-details-listing.component.css'],
  providers: [ComponentStore]
})

export class StockAvailabilityDetailsListingComponent extends ViewColumnComponent implements OnInit, OnDestroy {

  protected subs = new SubSink();
  compId = "stockAvailabilityDetails"
  compName = 'Stock Availability Details';

  protected readonly index = 0;
  readonly localState$ = this.viewColFacade.selectLocalState(this.index);
  protected localState: LocalState;

  protected hasRunInitialSearch = false;
  readonly deactivateAdd$ = this.componentStore.select(state => state.deactivateAdd);
  readonly deactivateList$ = this.componentStore.select(state => state.deactivateList);

  readonly masterSettings$ = this.sessionStore.select(
    SessionSelectors.selectMasterSettings
  );

  readonly clientSidePermissions$ = this.permissionStore.select(
    ClientSidePermissionsSelectors.selectAll
  );

  readonly userPermissionTarget$ = this.permissionStore.select(
    UserPermInquirySelectors.selectUserPermInquiry
  );

  readonly userRank$ = this.sessionStore.select(
    SessionSelectors.selectUserRank
  );

  readPermissionDefintion = {
    location: 'API_TNT_DM_ERP_INV_BATCH_READ'
  }

  showColumns = [
    { name: 'purchase_unit_price', setting: 'HIDE_LISTING_PURCHASE_PRICE', permission: 'SHOW_LISTING_PURCHASE_PRICE'},
    { name: 'sales_unit_price', setting: 'HIDE_LISTING_SALES_PRICE', permission: 'SHOW_LISTING_SALES_PRICE'},
    { name: 'sales_max_price', setting: 'HIDE_LISTING_SALES_MAX_PRICE', permission: 'SHOW_LISTING_SALES_MAX_PRICE'},
    { name: 'sales_min_price', setting: 'HIDE_LISTING_SALES_MIN_PRICE', permission: 'SHOW_LISTING_SALES_MIN_PRICE'},
    { name: 'replacement_unit_price_excl_tax', setting: 'HIDE_LISTING_REPLACEMENT_PRICE', permission: 'SHOW_LISTING_REPLACEMENT_PRICE'},
    { name: 'ref_price1_excl_tax', setting: 'HIDE_LISTING_REF_PRICE_1', permission: 'SHOW_LISTING_REF_PRICE_1'},
    { name: 'ref_price2_excl_tax', setting: 'HIDE_LISTING_REF_PRICE_2', permission: 'SHOW_LISTING_REF_PRICE_2'},
    { name: 'ref_price3_excl_tax', setting: 'HIDE_LISTING_REF_PRICE_3', permission: 'SHOW_LISTING_REF_PRICE_3'},
    { name: 'delta_price1_excl_tax', setting: 'HIDE_LISTING_DELTA_PRICE_1', permission: 'SHOW_LISTING_DELTA_PRICE_1'},
    { name: 'delta_price2_excl_tax', setting: 'HIDE_LISTING_DELTA_PRICE_2', permission: 'SHOW_LISTING_DELTA_PRICE_2'},
    { name: 'delta_price3_excl_tax', setting: 'HIDE_LISTING_DELTA_PRICE_3', permission: 'SHOW_LISTING_DELTA_PRICE_3'},
    { name: 'rebate_price1_excl_tax', setting: 'HIDE_LISTING_REBATE_PRICE_1', permission: 'SHOW_LISTING_REBATE_PRICE_1'},
    { name: 'rebate_price2_excl_tax', setting: 'HIDE_LISTING_REBATE_PRICE_2', permission: 'SHOW_LISTING_REBATE_PRICE_2'},
    { name: 'rebate_price3_excl_tax', setting: 'HIDE_LISTING_REBATE_PRICE_3', permission: 'SHOW_LISTING_REBATE_PRICE_3'}
  ];

  toggleColumn$: Observable<boolean>;
  searchModel = StockAvailabilityDetailsSearchModel;
  inputModel = {} as StockAvailabilityInputModel;
  optional = [];

  settings: any[] = [];
  permissions: any[] = [];
  rowData = [];
  totalRecords = 0;
  searchQuery: SearchQueryModel;

  increaseImageSize: boolean;
  masterSettings: any;

  expanded = false;

  // api visa
  apiVisa = AppConfig.apiVisa;

  // initial grid state
  gridApi;
  gridColumnApi;
 
  frameworkComponentsImage = {
    imageCellRenderer: ImageCellRendererLabel
  };

  gridOptions: GridOptions = {
    sideBar: {
      toolPanels: [
          {
              id: 'columns',
              labelDefault: 'Columns',
              labelKey: 'columns',
              iconKey: 'columns',
              toolPanel: 'agColumnsToolPanel',
              toolPanelParams: {
                  suppressPivotMode: true, // Suppress the pivot mode button
                  suppressRowGroups: true, // Suppress row grouping
                  suppressValues: true,   // Optionally suppress value columns
              }
          },
          {
              id: 'filters',
              labelDefault: 'Filters',
              labelKey: 'filters',
              iconKey: 'filter',
              toolPanel: 'agFiltersToolPanel'
          }
      ]
    },
    getRowHeight: (params) => {
      // Use a default value if increaseImageSize is not yet set
      const isLargeImageSize = this.increaseImageSize !== undefined ? this.increaseImageSize : false;
      return isLargeImageSize ? 155 : 30; // Set your default row height here
    },
    suppressAggFuncInHeader: true,
    groupDisplayType: 'multipleColumns',
    groupDefaultExpanded: 0,
    pivotMode: false,
    pagination: false,
    animateRows: true,
    rowGroupPanelShow: 'always',
    enableCharts: true,
    enableRangeSelection: true,
    rowSelection: 'multiple',
    suppressRowClickSelection: false,
    groupRemoveSingleChildren: false,
    groupHideOpenParents: false,
    frameworkComponents: this.frameworkComponentsImage,
    groupIncludeTotalFooter: true,
    tooltipShowDelay: 0,
    tooltipHideDelay: 99999999,
    onCellClicked: (event) => this.onCellClicked(event)
  };

  columnsDefs = [];

  constructor(
    protected readonly sessionStore: Store<SessionStates>,
    protected viewColFacade: ViewColumnFacade,
    protected apiService: ApiService,
    private listingService: ListingService,
    protected toastr: ToastrService,
    protected readonly store: Store<StockAvailabilityDetailsStates>,
    protected readonly componentStore: ComponentStore<LocalState>,
    protected readonly permissionStore: Store<PermissionStates>) {
    super();
  }

  ngOnInit() {
    this.toggleColumn$ = this.viewColFacade.toggleColumn$;
    this.subs.sink = this.viewColFacade.rowDataDetails$.subscribe(rowData => this.rowData = rowData);
    this.subs.sink = this.viewColFacade.totalRecordsDetails$.subscribe(totalRecords => this.totalRecords = totalRecords);

    this.subs.sink = this.localState$.subscribe(a => {
      this.localState = a;
      this.componentStore.setState(a);
    });

    this.getAppletSettings();
    this.getPermissions();

    this.subs.sink = this.sessionStore.select(SessionSelectors.selectPersonalSettings)
    .subscribe((data) => {
      if(data?.DEFAULT_TOGGLE_COLUMN){
        if(data.DEFAULT_TOGGLE_COLUMN === "SINGLE"){
          this.onToggle(true);
        }else{
          this.onToggle(false);
        }
      }
      else if (this.masterSettings?.DEFAULT_TOGGLE_COLUMN){
        if(this.masterSettings.DEFAULT_TOGGLE_COLUMN === "SINGLE"){
          this.onToggle(true);
        }else{
          this.onToggle(false);
        }
      }
    })
  }

  onGridReady(params) {
    this.gridApi = params.api;
    this.gridColumnApi = params.columnApi;
    this.gridApi.closeToolPanel();

    // Status bar is disabled, so no need to configure status bar components

    params.api.addEventListener('cellClicked', (event) => {
      if (event.event?.target && event.event.target.classList.contains('movement-link')) {
        this.openMovement(event.data); // Pass row data if needed
      }
      if (event.event?.target && event.event.target.classList.contains('sales-report-link')) {
        this.openSalesReport(event.data); // Pass row data if needed
      }
    });

    if (this.rowData.length > 0) {
      this.setDataRowCache();
    }

    if (!this.hasRunInitialSearch) {
      this.hasRunInitialSearch = true;
      this.subs.sink = this.sessionStore
        .select(SessionSelectors.selectPersonalSettings)
        .pipe(take(1))
        .subscribe((settings) => {
          const saved = settings?.stockAvailabilityDetailsAdvancedSearch;
          if (saved) {
            const queryObject: any = {
              itemCode: saved.itemCode || '',
              location: saved.location || [],
              itemType: saved.itemType || [],
              itemStatus: saved.itemStatus || ['ACTIVE'],
              optional: saved.optional || [],
              itemRange: saved.itemRange || { from: null, to: null },
              itemRangeCheckbox: saved.itemRangeCheckbox || false,
              locationLabel: saved.locationLabel || [],
            };

            for (let i = 1; i <= 20; i++) {
              queryObject[`itemCategoryLevel${i}`] = saved[`itemCategoryLevel${i}`] || [];
            }

            const initialSearchQuery: SearchQueryModel = {
              isEmpty: false,
              keyword: saved.itemCode || '',
              queryString: queryObject
            };

            this.onSearch(initialSearchQuery);
          }
        });
    }
  }
  
  createData(inputModel?: StockAvailabilityInputModel) {
    this.viewColFacade.loadInit(this.gridApi);
    this.subs.sink = this.apiService.getStockAvailabilityDetails(inputModel, this.apiVisa).pipe(
      mergeMap(a => from(a.data).pipe(
        map(b => {
          b.imageData = b.main_image_guid;
          Object.assign(b,
            {
              pricingSchemes: this.getPricingSchemes(b['pricingSchemes']),
              total_balance: this.getTotalBalance(b['locationRows']),
              total_so_qty: this.getTotalSO(b['locationRows']),
              hide_grn_balance: this.isHideGRNBalance(),
              hide_po_balance: this.isHidePOBalance(),
              hide_so_balance: this.isHideSOBalance(),
              main_image: b.main_image_guid,
            },
            ...Object.keys(b.locationRows).map(locationKey => {
              const locationData = b.locationRows[locationKey];
              if (!locationData.grn_qty) locationData.grn_qty = 0;
              return {
                [`${locationData.location_guid}_balance`]: this.getLocationBalance(locationData),
                [`${locationData.location_guid}_name`]: locationData.name
              };
            }),

            ...(Array.isArray(this.masterSettings.PRICING_SCHEMES)
              ? this.masterSettings.PRICING_SCHEMES.map(guid => {
                return {
                  [`${guid}_price_metric`]: this.getPriceMetric(b, guid),
                  reward_point: this.getPriceMetric(b, guid, 'reward_point'),
                  reward_point_ratio: this.getPriceMetric(b, guid, 'reward_point_ratio'),
                  redeem_point: this.getPriceMetric(b, guid, 'redeem_point'),
                  redeem_point_ratio: this.getPriceMetric(b, guid, 'redeem_point_ratio')
                };
              }):[])
          )
          return b; 
        }),
        toArray(),
        map(c => {
          a.data = c;
          return a;
        })
      ))
    ).subscribe(resolved => {
      //console.log("resolved-------->",resolved);
      this.viewColFacade.loadSuccess(resolved);
      this.totalRecords = resolved.data.length;

      this.rowData = this.checkZeroBalance(resolved.data);
      //console.log("rowData", this.rowData)
      this.gridApi.setRowData(this.rowData);
      this.viewColFacade.selectRowDataDetails(this.rowData);
      this.viewColFacade.selectTotalRecordsDetails(this.totalRecords);

      this.createDynamicLocationColumns(this.rowData);
      this.showTotalSOColumn();
    }, err => {
      if(err.status===403)
      {
        this.viewColFacade.handle403Error(err,'POST')
      }
      else{
      this.toastr.error(
        err.message,
        'AG Gird Error',
        {
          tapToDismiss: true,
          progressBar: true,
          timeOut: 1300
        }
      );
    }
      this.viewColFacade.loadFailed(err);
    });
  };

  onToggle(e: boolean) {
    this.viewColFacade.toggleColumn(e);
  }

  onRowClicked(entity: StockAvailabilityModel) {
    //console.log('onRowClicked', entity);
  }

  onSearch(e: SearchQueryModel) {
    let canSearch = false;
    this.clear();
    if (!e.isEmpty) {
      if (e.keyword && e.keyword.length > 0 && e.keyword.length < 3)  {
        this.toastr.error(
          'Search item code must more than 2 characters.',
          'Item Code',
          {
            tapToDismiss: true,
            progressBar: true,
            timeOut: 1300
          }
        );
        return;
      }

      this.inputModel.keyword = e.keyword;
      if (e.queryString) {
        this.inputModel.keyword = UtilitiesModule.checkNull(e.queryString['itemCode'],'');
        this.inputModel.location_guids = UtilitiesModule.checkNull(e.queryString['location'], []);
        this.inputModel.item_type = UtilitiesModule.checkNull(e.queryString['itemType'],[]);
        this.inputModel.item_status = UtilitiesModule.checkNull(e.queryString['itemStatus'],[]);
        this.optional = UtilitiesModule.checkNull(e.queryString['optional'],[]);

        for (let i = 1; i <= 20; i++) {
          const categoryKey = `item_category${i}_guids`;
          this.inputModel[categoryKey] = UtilitiesModule.checkNull(e.queryString[`itemCategoryLevel${i}`], []);
          if (this.inputModel[categoryKey] && this.inputModel[categoryKey].length > 0) {
            canSearch = true;
          }
        }

        if (e.queryString['itemRangeCheckbox'])  {
          const itemFrom = e.queryString['itemRange']['from'];
          this.inputModel.item_from = itemFrom;
          const itemTo = e.queryString['itemRange']['to'];
          this.inputModel.item_to = itemTo;
          canSearch = true;
        }

      }

      const locationLabels = UtilitiesModule.checkNull(e.queryString['locationLabel'], []);
      if (locationLabels && locationLabels.length > 0) {
        const guidHdrList = locationLabels.flatMap(item =>
          item.bl_fi_location_label_link ? item.bl_fi_location_label_link.map(link => link.guid_hdr) : []
        );
        this.inputModel.location_guids = [...this.inputModel.location_guids, ...guidHdrList];
      }
      if (this.locationNotSelected())  {
        this.toastr.error(
          'Please select location.',
          'Error',
          {
            tapToDismiss: true,
            progressBar: true,
            timeOut: 1300
          }
        );
        return;
      }



      if (!this.inputModel.keyword && !canSearch)  {
        this.toastr.error(
          'Please enter item code or select at least one category.',
          'Error',
          {
            tapToDismiss: true,
            progressBar: true,
            timeOut: 1300
          }
        );
        return;
      }

      this.createData(this.inputModel);
    }
  }

  clear() {
    this.gridApi.setRowData(null);
    this.totalRecords = 0;
    this.rowData = [];
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }

  setDataRowCache() {
    this.gridApi.setRowData(this.rowData);
    this.createDynamicLocationColumns(this.rowData);;
  }

  getRowStyle = params => {
    if (params.data && params.data.status && 'INACTIVE' === params.data.status) {
      return { background: 'red' };
    }
    if (params.node.footer) {
      return { fontWeight: 'bold', background: '#e6f7ff' };
    }
    if (params.node.group) {
      return { fontWeight: 'bold' };
    }
  }

  getPricingSchemes(pricingSchemes) {
    const transformedData = Object.keys(pricingSchemes).map(key => pricingSchemes[key]);
    return transformedData;
  }

  getLocationRows(locationRows) {
    const transformedData = Object.keys(locationRows).map(key => locationRows[key]);
    return transformedData;
  }

  getTotalBalance(locationRows) {
    let totalBalance = 0;
    Object.keys(locationRows).forEach(locationGuid => {
      const locationRow = locationRows[locationGuid];
      totalBalance += <number>locationRow.balance;
      if (!this.isHideGRNBalance()) {
        totalBalance += <number>locationRow.grn_qty;
      }
      if (!this.isHidePOBalance()) {
        totalBalance += <number>locationRow.po_qty;
      }
      if (!this.isHideSOBalance()) {
        totalBalance -= <number>locationRow.so_qty;
      }
      //totalBalance = totalBalance + <number>locationRow.balance + <number>locationRow.grn_qty + <number>locationRow.po_qty - <number>locationRow.so_qty;
    });
    return totalBalance;
  }

  getLocationBalance(locationRow) {
    let totalBalance = 0;
    totalBalance += <number>locationRow.balance;
    if (!this.isHideGRNBalance()) {
      totalBalance += <number>locationRow.grn_qty;
    }
    if (!this.isHidePOBalance()) {
      totalBalance += <number>locationRow.po_qty;
    }
    if (!this.isHideSOBalance()) {
      totalBalance -= <number>locationRow.so_qty;
    }
    return totalBalance;
  }

  getTotalSO(locationRows) {
    let totalBalance = 0;
    Object.keys(locationRows).forEach(locationGuid => {
      const locationRow = locationRows[locationGuid];
      totalBalance += <number>locationRow.so_qty;
    });
    return totalBalance;
  }

  async createDynamicLocationColumns(dataRow) {
    let existingColDefs = this.gridApi.getColumnDefs();
    let existingColIds = new Set(existingColDefs.map(col => col.colId));
    const uniqueLocations = {};

    //Add pricing scheme columns
    try {
        existingColDefs = existingColDefs.filter(col => col.colId !== 'pricing-scheme');
        const colDefs = await this.getPricingSchemeColumns(this.masterSettings);
        existingColDefs = [...existingColDefs, ...colDefs];
    } catch (error) {
      this.viewColFacade.handle403Error(error,'GET')
        console.error('Error fetching pricing scheme columns:', error);
    }

    // Add a unique column for the total balance
    if (!existingColIds.has('total_balance')) {
      existingColDefs.push({
        colId: 'total_balance',
        headerClass: 'ag-center-aligned-header',
        headerName: 'Total',
        field: 'total_balance',
        type: 'integerCenterColumn',
        cellRenderer: TotalBalanceCellRendererComponent,
        autoHeight: true,
      });
    }

    // Extract and sort locationRows by location_code
    let sortedLocationRows = [];
    dataRow.forEach(row => {
      Object.keys(row.locationRows).forEach(locationGuid => {
        const locationRow = row.locationRows[locationGuid];
        sortedLocationRows.push(locationRow);
      });
    });

    sortedLocationRows.sort((a, b) => {
      if (a.location_code < b.location_code) return -1;
      if (a.location_code > b.location_code) return 1;
      return 0;
    });

    // Add sorted locations to existingColDefs
    sortedLocationRows.forEach(locationRow => {
      const colId = locationRow.location_guid;

      if (!existingColIds.has(colId)) {
        existingColIds.add(colId); // add to prevent duplicates

        // Check if the location_guid has already been added
        if (!uniqueLocations[locationRow.location_guid]) {
          uniqueLocations[locationRow.location_guid] = true;

          existingColDefs.push({
            colId: locationRow.location_guid,
            headerClass: 'ag-center-aligned-header',
            headerName: `${locationRow.location_code}`,
            field: `${locationRow.location_guid}_balance`,
            //tooltipField: `${locationRow.location_guid}_balance`,
            tooltipValueGetter: params => {
              // Return null if the row is a footer row
              if (params.node.footer) {
                return null;
              }
              return { value: params.value };
            },
            tooltipComponent: CustomTooltip,
            tooltipComponentParams: {
              location_guid: `${locationRow.location_guid}`,
              location_name: `${locationRow.location_name}`,
            },
            type: 'integerCenterColumn',
            cellRenderer: MultilineCellRendererComponent,
            cellRendererParams: { location_guid: `${locationRow.location_guid}`},
            autoHeight: true,
          });
        }
      }
    });

    this.gridApi.setColumnDefs(existingColDefs);
  }

  onCellClicked(e) {
    const locationGuid = e.column.colId;
    if (!UtilitiesModule.isValidGuid(locationGuid)) {
      return;
    }

    let entity;
    if (e.data) {
      entity = e.data;
    }
    if (entity) {
      let entityData = this.moveLocationData(entity, locationGuid);
      this.viewColFacade.selectGuid(entityData.inv_item_guid.toString());
      this.viewColFacade.selectEntity(entityData);

      if (!this.localState.deactivateList) {
        this.viewColFacade.updateInstance(this.index, {
          ...this.localState, deactivateAdd: true, deactivateList: true
        });
        this.viewColFacade.onNextAndReset(this.index, 1);
      }
    }
  }

  moveLocationData(data, locationGuid) {
    const clonedData = JSON.parse(JSON.stringify(data));
    if (clonedData.locationRows && clonedData.locationRows[locationGuid]) {
      const locationData = clonedData.locationRows[locationGuid];

      // Remove locationRows property
      delete clonedData.locationRows;

      // Move data to the main object
      clonedData.location_guid = locationGuid;
      clonedData.location_code = locationData.location_code;
      clonedData.location_name = locationData.location_name;
      clonedData.balance = locationData.balance;
      clonedData.grn_qty = locationData.grn_qty;
      clonedData.po_qty = locationData.po_qty;
      clonedData.so_qty = locationData.so_qty;
      clonedData.cost_ma_price = locationData.cost_ma_price;
      clonedData.cost_last_purchase = locationData.cost_last_purchase;
      clonedData.cost_fifo = locationData.cost_fifo;
      clonedData.cost_lifo = locationData.cost_lifo;
    }
    return clonedData;
  }

  async getAppletSettings() {
    this.subs.sink = await this.masterSettings$.subscribe(
      resolve => {
        this.masterSettings = resolve;
        this.settings.push(resolve);
        this.increaseImageSize = this.masterSettings.INCREASE_ITEM_IMAGE_SIZE;
        this.initializeColumnDefs();
        setTimeout(() => {
          this.gridOptions.api?.resetRowHeights(); // Reset row heights after settings change
        }, 0);
      }
    );
  }

  initializeColumnDefs() {
    this.columnsDefs = [
      {
        headerName: 'Action',
        field: 'movement',
        cellRenderer: (params: any) => {
          if (params.node.group || params.node.footer) {
            return '';
          }
          return `
            <div style="display: flex; gap: 8px;">
              <i class="material-icons movement-link" style="color: blue; cursor: pointer;" title="View Movement">
                sync_alt
              </i>
              <i class="material-icons sales-report-link" style="color: green; cursor: pointer;" title="View Sales Report">
                assessment
              </i>
            </div>
          `;
        },
        cellRendererParams: {
          onClick: this.openMovement.bind(this)  // Pass the parent function
        },
        filter: false,
        width: 100,
        minWidth: 100
      },
      {
        headerName: 'Image',
        field: 'imageData',
        cellRenderer: 'imageCellRenderer',
        cellRendererParams: {
          isLarge: this.increaseImageSize, // Set to true for 150x150, false for 50x50
        },
        filter: false,
        minWidth: 160,
        maxWidth: 220,
      },
      {
        headerName: 'Item Code',
        field: 'item_code',
        //cellRenderer: 'agGroupCellRenderer',
        type: 'textColumn'
      },
      {
        headerName: 'Inv Item Code',
        field: 'inv_item_code',
        hide: true,
        type: 'textColumn'
      },
      {
        headerName: 'Item Name',
        field: 'item_name',
        type: 'textColumn'
      },
      {
        headerName: 'Item Description',
        field: 'item_descr',
        hide: true,
        type: 'textColumn'
      },
      {
        headerName: 'Reward Point',
        field: 'reward_point',
        hide: true,
        type: 'decimalColumn'
      },
      {
        headerName: 'Reward Point Ratio',
        field: 'reward_point_ratio',
        hide: true,
        type: 'decimalColumn'
      },
      {
        headerName: 'Redeem Point',
        field: 'redeem_point',
        hide: true,
        type: 'decimalColumn'
      },
      {
        headerName: 'Redeem Point Ratio',
        field: 'redeem_point_ratio',
        hide: true,
        type: 'decimalColumn'
      },
      {
        headerName: 'EAN Code',
        field: 'scan_code',
        hide: true,
        type: 'textColumn'
      },
      {
        headerName: 'Type',
        field: 'type',
        hide: true,
        type: 'textColumn'
      },
      {
        headerName: 'Sub Type',
        field: 'sub_type',
        hide: true,
        type: 'textColumn'
      },
      {
        headerName: 'UOM',
        field: 'uom',
        hide: true,
        type: 'textColumn'
      },
      {
        headerName: 'Status',
        field: 'status',
        hide: true,
        type: 'textColumn'
      },
      {
        headerName: 'Job Order Availability',
        field: 'job_order_qty',
        hide: true,
        type: 'textColumn'
      },
      {
        headerName: 'Inv Item Description',
        field: 'inv_item_desc',
        hide: true,
        type: 'textColumn'
      },
      {
        headerName: 'Total SO qty',
        field: 'total_so_qty',
        hide: false,
        type: 'integerCenterColumn'
      },
      { headerName: 'Category 0', field: 'label_hdr_0_code', type: 'textColumn',  hide: true },
      { headerName: 'Category 1', field: 'label_hdr_1_code', type: 'textColumn',  hide: false },
      { headerName: 'Category 2', field: 'label_hdr_2_code', type: 'textColumn',  hide: true },
      { headerName: 'Category 3', field: 'label_hdr_3_code', type: 'textColumn',  hide: true },
      { headerName: 'Category 4', field: 'label_hdr_4_code', type: 'textColumn',  hide: true },
      { headerName: 'Category 5', field: 'label_hdr_5_code', type: 'textColumn',  hide: true },
      { headerName: 'Category 6', field: 'label_hdr_6_code', type: 'textColumn',  hide: true },
      { headerName: 'Category 7', field: 'label_hdr_7_code', type: 'textColumn',  hide: true },
      { headerName: 'Category 8', field: 'label_hdr_8_code', type: 'textColumn',  hide: true },
      { headerName: 'Category 9', field: 'label_hdr_9_code', type: 'textColumn',  hide: true },
      { headerName: 'Category 10', field: 'label_hdr_10_code', type: 'textColumn',  hide: true },
      { headerName: 'Category 11', field: 'label_hdr_11_code', type: 'textColumn',  hide: true },
      { headerName: 'Category 12', field: 'label_hdr_12_code', type: 'textColumn',  hide: true },
      { headerName: 'Category 13', field: 'label_hdr_13_code', type: 'textColumn',  hide: true },
      { headerName: 'Category 14', field: 'label_hdr_14_code', type: 'textColumn',  hide: true },
      { headerName: 'Category 15', field: 'label_hdr_15_code', type: 'textColumn',  hide: true },
      { headerName: 'Category 16', field: 'label_hdr_16_code', type: 'textColumn',  hide: true },
      { headerName: 'Category 17', field: 'label_hdr_17_code', type: 'textColumn',  hide: true },
      { headerName: 'Category 18', field: 'label_hdr_18_code', type: 'textColumn',  hide: true },
      { headerName: 'Category 19', field: 'label_hdr_19_code', type: 'textColumn',  hide: true },
      { headerName: 'Category 20', field: 'label_hdr_20_code', type: 'textColumn',  hide: true },
    ];
  }

  async getPermissions() {
    this.subs.sink = await this.clientSidePermissions$.subscribe(
      resolve => {
        this.permissions = resolve;
      }
    );
  }

  isShowColumn(settingName, permissionName) {
    return !this.settings.some(setting => setting[settingName]) || this.permissions.some(permission => permission.perm_code === permissionName);
  }

  setColumnVisible(gridApi, gridColumnApi, column: any, visible: boolean = false) {
    var colDef = column.getColDef();
    colDef.suppressColumnsToolPanel = !visible;
    gridColumnApi.setColumnVisible(column, visible)
    gridApi.refreshToolPanel();
  }

  isHideZeroBalance() {
    return (this.optional.indexOf('HIDE_ZERO_BALANCE') >= 0);
  }

  isHideGRNBalance() {
    return (this.optional.indexOf('HIDE_GOODS_RECEIVE_NOTE_BALANCE') >= 0);
  }

  isHidePOBalance() {
    return (this.optional.indexOf('HIDE_PURCHASE_ORDER_BALANCE') >= 0);
  }

  isHideSOBalance() {
    return (this.optional.indexOf('HIDE_SALES_ORDER_BALANCE') >= 0);
  }

  showNotes() {
    //Noted: Stock Balance / Goods Received Note Balance / Purchase Order Balance / Sales Order Balance
    let notes = [];
    let noted = '';
    if (!this.isHideGRNBalance()) {
      notes.push('Goods Received Note Balance')
    }
    if (!this.isHidePOBalance()) {
      notes.push('Purchase Order Balance')
    }
    if (!this.isHideSOBalance()) {
      notes.push('Sales Order Balance')
    }

    if (notes.length > 0) {
      noted = 'Stock Balance';
      notes.forEach(n => {
        noted += ' / ' + n;
      });
    }

    return noted;
  }

  checkZeroBalance(docRow) {
    if (this.isHideZeroBalance()) {
      const returnDataRow = [];

      docRow.forEach(row => {
        let hasValidLocation = false;  // Start as false for each row

        for (const key in row.locationRows) {
          if (row.locationRows.hasOwnProperty(key)) {
            const locRow = row.locationRows[key];

            // Check the validity based on the hide flags
            const isBalanceValid = Number(locRow.balance) !== 0;
            const isGRNValid = !this.isHideGRNBalance() && Number(locRow.grn_qty) !== 0;
            const isPOValid = !this.isHidePOBalance() && Number(locRow.po_qty) !== 0;
            const isSOValid = !this.isHideSOBalance() && Number(locRow.so_qty) !== 0;

            // Determine if there is a valid location
            if (this.isHideGRNBalance() && this.isHidePOBalance() && this.isHideSOBalance()) {
              // All are hidden: Only check balance
              hasValidLocation = isBalanceValid;
            } else {
              // At least one is visible: Check balance and non-hidden quantities
              hasValidLocation = isBalanceValid || isGRNValid || isPOValid || isSOValid;
            }

            // If a valid location is found, break out of the loop
            if (hasValidLocation) {
              break;
            }
          }
        }

        // Only add the row if at least one location row is valid
        if (hasValidLocation) {
          returnDataRow.push(row);
        }
      });

      return returnDataRow;
    } else {
      return docRow;  // Return all rows if hiding is not applied
    }
  }



  async getPricingSchemeColumns(masterSettings): Promise<any[]> {
    const guids = masterSettings.PRICING_SCHEMES;
    if (!guids || guids.length === 0) return [];

    const inputModel = {} as ListingInputModel;
    inputModel.search = "";
    inputModel.searchColumns = ['code', 'name'];
    inputModel.status = ['ACTIVE'];
    inputModel.orderBy = 'code';
    inputModel.order = 'asc';
    inputModel.limit = null;
    inputModel.offset = 0;
    inputModel.calcTotalRecords = false;
    inputModel.showCreatedBy = false;
    inputModel.showUpdatedBy = false;
    inputModel.filterLogical = 'AND';
    inputModel.filterConditions = [
      {
        "filterColumn": "guid",
        "filterValues": guids,
        "filterOperator": "IN"
      }
    ];

    return new Promise((resolve, reject) => {
        this.listingService.get("pricing-scheme", inputModel, this.apiVisa).pipe(
        ).subscribe(resolved => {
            const newColDefs = [];
            if (resolved.data) {
                resolved.data.forEach(ps => {
                    newColDefs.push({
                        colId: 'pricing_scheme',
                        headerName: ps.name,
                        field: `${ps.guid}_price_metric`,
                        tooltipValueGetter: params => {
                          // Return null if the row is a footer row
                          if (params.node.footer) {
                            return null;
                          }
                            return { value: params.value };
                        },
                        tooltipComponent: PricingSchemeTooltip,
                        tooltipComponentParams: {
                          guid: `${ps.guid}`,
                        },
                        type: 'decimalColumn'
                    });
                }); 
            }
            resolve(newColDefs);
        }, error => {
          this.viewColFacade.handle403Error(error,'GET')
            reject(error);
        });
    });
  }

  getPriceMetric(item, pricing_scheme_guid, pricing_scheme_column?) {
    let pricingMetrics = pricing_scheme_column 
      ?? this.masterSettings?.PRICE_METRICS?.toLowerCase() 
      ?? 'sales_unit_price';
    if (item.pricingSchemes && item.pricingSchemes[pricing_scheme_guid]) {
      return item.pricingSchemes[pricing_scheme_guid][pricingMetrics];
    }

    return 0; // Return null if the pricing scheme is not found
  }

  locationNotSelected() {
    return (!this.inputModel.location_guids || this.inputModel.location_guids.length == 0);
  }

  showTotalSOColumn() {
    const column = this.gridOptions.columnApi?.getColumn('total_so_qty');
    if (column) {
      this.setColumnVisible(this.gridOptions.api, this.gridOptions.columnApi, column, !this.isHideSOBalance());
    }
  }

  toggleExpandAll() {
    this.expanded = !this.expanded; // Toggle the expanded state
    this.gridApi.forEachNode((node: any) => {
      if (node.master) {
        node.setExpanded(this.expanded);
      }
    });
  }

  openMovement(entity: any) {
    //console.log('Opening movement for:', entity);

    if (entity) {
      this.viewColFacade.selectGuid(entity.inv_item_guid.toString());
      this.viewColFacade.selectEntity(entity);

      if (!this.localState.deactivateList) {
        this.viewColFacade.updateInstance(this.index, {
          ...this.localState, deactivateAdd: true, deactivateList: true
        });
        this.viewColFacade.onNextAndReset(this.index, 3);
      }
    }
  }

  openSalesReport(entity: any) {
    //console.log('Opening sales report for:', entity);

    if (entity) {
      this.viewColFacade.selectGuid(entity.inv_item_guid.toString());
      this.viewColFacade.selectEntity(entity);

      if (!this.localState.deactivateList) {
        this.viewColFacade.updateInstance(this.index, {
          ...this.localState, deactivateAdd: true, deactivateList: true
        });
        this.viewColFacade.onNextAndReset(this.index, 4); // Navigate to index 4 for sales report
      }
    }
  }

}

