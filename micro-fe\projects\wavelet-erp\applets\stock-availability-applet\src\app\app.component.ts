import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { mainPath } from './app.routing';
import { menuItems } from './models/menu-items';
import { PermissionStates } from 'projects/shared-utilities/modules/permission/permission-controller';
import { UserPermInquriyActions } from 'projects/shared-utilities/modules/permission/user-permissions-inquiry-controller/actions';
import { ClientSidePermissionsSelectors } from "projects/shared-utilities/modules/permission/client-side-permissions-controller/selectors";
import { SubSink } from "subsink2";
import { combineLatest } from 'rxjs';
import { Store } from '@ngrx/store';
import { SessionSelectors } from 'projects/shared-utilities/modules/session/session-controller/selectors';
import { SessionStates } from 'projects/shared-utilities/modules/session/session-controller/states';

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.scss']
})
export class AppComponent implements OnInit {
  readonly appletName = 'Stock Availability';
  menuItems = menuItems;
  readonly mainPath = mainPath;

  protected subs = new SubSink();

  constructor(
    private router: Router,
    private readonly sessionStore: Store<SessionStates>,
    private readonly permissionStore: Store<PermissionStates>,) {
  }

  masterSettings$ = this.sessionStore.select(
    SessionSelectors.selectMasterSettings
  );

  clientSidePermissions$ = this.permissionStore.select(
    ClientSidePermissionsSelectors.selectAll
  );
  appletSettings;

  ngOnInit() {
    this.subs.sink = combineLatest([
      this.clientSidePermissions$,
      this.masterSettings$
    ]).subscribe({
      next: ([permissions, appletSettings]) => {
        const permissionCodes = new Set(
          permissions.map(p => p.perm_code)
        );

        this.appletSettings = appletSettings;

        const filteredMenu: typeof menuItems = [];

        for (const item of menuItems) {
          if (!item.state) {
            filteredMenu.push(item);
            continue;
          }

          const key = this.buildMenuKey(item.state);
          const showKey = `SHOW_${key}_MENU`;
          const hideKey = `HIDE_${key}_MENU`;

          // Store permission in class for reference (optional)
          (this as any)[showKey] = permissionCodes.has(showKey);

          const shouldHide = !permissionCodes.has(showKey) && appletSettings?.[hideKey];

          if (!shouldHide) {
            filteredMenu.push(item);
          }
        }

        this.menuItems = filteredMenu;
        if (filteredMenu.length > 1 && filteredMenu[1].state) {
          const path = [`${this.mainPath}/${filteredMenu[1].state}`];
          //console.log('path', path);
          this.router.navigate(path);
        }
      }
    });

    this.router.initialNavigation();

    let dto = {
      app_permission_dto: [
        {
          permDfn: "API_TNT_DM_ERP_INV_BATCH_READ",
        },
        {
          permDfn: "API_TNT_DM_ERP_INV_STOCK_AVAILABILITY_READ",
        },
        {
          permDfn: "API_TNT_DM_ERP_STOCK_AGING_REPORT_READ",
        },
        {
          permDfn: "API_TNT_DM_ERP_STOCK_REPORT_READ",
        },
        {
          permDfn: "TNT_API_BIN_READ",
        },
        {
          permDfn: "TNT_TENANT_ADMIN",
        },
        {
          permDfn: "TNT_TENANT_OWNER",
        },
      ],
    };

    this.permissionStore.dispatch(
      UserPermInquriyActions.loadUserPermissionInquiryInit({
        request: dto,
      })
    );
  }

  buildMenuKey(state: string) {
    return state.toUpperCase().replace(/-/g, '_');
  }
}
