.gross-profit-container {
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  margin: 16px 0;

  .header-section {
    margin-bottom: 24px;

    .component-title {
      margin: 0 0 16px 0;
      color: #2c3e50;
      font-size: 1.5rem;
      font-weight: 600;
    }

    .summary-cards {
      display: flex;
      gap: 16px;
      flex-wrap: wrap;

      .summary-card {
        background: white;
        border-radius: 8px;
        padding: 16px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        min-width: 150px;
        flex: 1;

        .card-label {
          font-size: 0.875rem;
          color: #6c757d;
          margin-bottom: 8px;
          font-weight: 500;
        }

        .card-value {
          font-size: 1.25rem;
          font-weight: 700;
          color: #2c3e50;

          &.positive {
            color: #28a745;
          }

          &.negative {
            color: #dc3545;
          }
        }
      }
    }
  }

  .grid-container {
    background: white;
    border-radius: 8px;
    padding: 16px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    .ag-theme-balham {
      border-radius: 4px;
      border: 1px solid #e9ecef;
    }
  }
}

// AG Grid custom styles for profit indicators
::ng-deep {
  .profit-positive {
    color: #28a745 !important;
    font-weight: 600;
  }

  .profit-negative {
    color: #dc3545 !important;
    font-weight: 600;
  }

  .ag-header-cell-label {
    font-weight: 600;
  }

  .ag-row:hover {
    background-color: #f8f9fa !important;
  }

  .ag-row-selected {
    background-color: #e3f2fd !important;
  }
}

// Responsive design
@media (max-width: 768px) {
  .gross-profit-container {
    padding: 12px;
    margin: 12px 0;

    .header-section {
      .summary-cards {
        flex-direction: column;

        .summary-card {
          min-width: unset;
        }
      }
    }

    .grid-container {
      padding: 12px;

      .ag-theme-balham {
        height: 300px !important;
      }
    }
  }
}
