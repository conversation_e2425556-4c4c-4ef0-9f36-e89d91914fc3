{"name": "akaun-app", "version": "3.6.2", "scripts": {"postinstall": "ngcc", "ng": "ng", "start:Shell": "ng s akaun-shell", "sme": "source-map-explorer", "start": "ng serve akaun-shell", "build": "ng build pwa-my-celllabs-ecom --prod", "build:elements": "ng build --prod --project=auth-applet --output-hashing none && node elements-build.js", "build:example-applet-elements": "ng build --prod --project=example-applet --output-hashing none && node elements-build-scripts/example/example-applet-elements-build.js", "build:pricebook-applet-elements": "ng build --prod --project=pricebook-applet --output-hashing none && node elements-build-scripts/wavelet-erp/pricebook-applet/pricebook-applet-elements-build.js", "build:shipping-pricebook-applet-elements": "ng build --prod --project=shipping-pricebook-applet --output-hashing none && node elements-build-scripts/wavelet-erp/shipping-pricebook-applet/shipping-pricebook-applet-elements-build.js", "build:recurring-sales-invoice-applet-elements": "ng build --prod --project=recurring-sales-invoice-applet --output-hashing none && node elements-build-scripts/wavelet-erp/recurring-sales-invoice-applet/recurring-sales-invoice-applet-elements-build.js", "build:delivery-installation-applet-elements": "ng build --prod --project=delivery-installation-applet --output-hashing none && node elements-build-scripts/wavelet-erp/delivery-installation-applet/delivery-installation-applet-elements.js", "build:bank-recon-applet-elements": "ng build --prod --project=bank-recon-applet --output-hashing none && node elements-build-scripts/wavelet-erp/bank-recon-applet/bank-recon-applet-elements-build.js", "build:txn-recon-applet-elements": "ng build --prod --project=txn-recon-applet --output-hashing none && node elements-build-scripts/wavelet-erp/txn-recon-applet/txn-recon-applet-elements-build.js", "build:tax-config-applet-elements": "ng build --prod --project=tax-config-applet --output-hashing none && node elements-build-scripts/wavelet-erp/tax-config-applet/tax-config-applet-elements-build.js", "build:stock-adjustment-applet-elements": "ng build --prod --project=stock-adjustment-applet --output-hashing none && node elements-build-scripts/wavelet-erp/stock-adjustment-applet/stock-adjustment-applet-elements-build.js", "test": "ng test", "lint": "ng lint", "prod": "node --max-old-space-size=6144./node_modules/@angular/cli/bin/ng build --prod", "e2e": "ng e2e", "bundle-report-akaun": "webpack-bundle-analyzer dist/pwa-akaun-platform/stats.json", "bundle-report-celllabs": "webpack-bundle-analyzer dist/pwa-my-celllabs-ecom/stats.json", "clb": "ng serve pwa-my-celllabs-ecom --sourceMap false --disable-host-check", "clb2": "ng serve pwa-my-celllabs-ecom --sourceMap false --port 4500", "qr": "ng serve pwa-my-celllabs-qr --sourceMap false --port 4800", "akaun": "./bin/clb_serve.sh", "build:stats": "ng build akaun-shell --stats-json", "analyze": "webpack-bundle-analyzer dist/akaun-shell/stats-es2015.json", "ng_serve_beauty_pos_applet": "ng s beauty-pos-applet --configuration=staging", "ng_serve_grn_applet": "ng s grn-applet --configuration=staging", "inv": "ng s inv-item-maintenance-applet", "asset": "ng s asset-register-applet", "fatal": "export NODE_OPTIONS='--max-old-space-size=5120'", "deploy_inv": "./bin/all-inv-item-maintenance_publish.sh", "deploy_asset": "./bin/all-asset-register_publish.sh", "extract-i18n": "ngx-translate-extract --input ./projects/wavelet-erp/applets/internal-sales-invoice-applet --output ./src/assets/i18n/template.json"}, "private": true, "dependencies": {"@ag-grid-community/all-modules": "^27.3.0", "@ag-grid-community/angular": "^27.3.0", "@ag-grid-enterprise/excel-export": "^27.3.0", "@angular-builders/custom-webpack": "^11.1.1", "@angular-devkit/build-angular": "^0.1102.14", "@angular-material-components/color-picker": "^6.0.0", "@angular-material-components/datetime-picker": "^5.1.1", "@angular-material-components/moment-adapter": "^5.0.0", "@angular/animations": "^11.2.14", "@angular/cdk": "^11.2.13", "@angular/common": "^11.2.14", "@angular/compiler": "^11.2.14", "@angular/compiler-cli": "^11.2.14", "@angular/core": "^11.2.14", "@angular/elements": "^11.2.14", "@angular/flex-layout": "^11.0.0-beta.33", "@angular/forms": "^11.2.14", "@angular/material": "^11.2.13", "@angular/material-moment-adapter": "^11.2.13", "@angular/platform-browser": "^11.2.14", "@angular/platform-browser-dynamic": "^11.2.14", "@angular/platform-server": "^11.2.14", "@angular/router": "^11.2.14", "@biesbjerg/ngx-translate-extract": "^7.0.4", "@capacitor/core": "^2.4.8", "@fullcalendar/angular": "5.2", "@fullcalendar/core": "5.2", "@fullcalendar/daygrid": "5.2", "@fullcalendar/interaction": "5.2", "@fullcalendar/list": "5.2", "@fullcalendar/timegrid": "5.2", "@ngrx/component-store": "^10.1.2", "@ngrx/effects": "11.1.1", "@ngrx/entity": "11.1.1", "@ngrx/router-store": "^6.1.0", "@ngrx/store-devtools": "11.1.1", "@nguniversal/express-engine": "11.2.1", "@ngx-pwa/local-storage": "^6.1.0", "@ngx-translate/core": "^13.0.0", "@ngx-translate/http-loader": "^6.0.0", "@progress/kendo-angular-buttons": "^6.2.0", "@progress/kendo-angular-common": "^2.0.0", "@progress/kendo-angular-dateinputs": "^5.2.1", "@progress/kendo-angular-dialog": "^5.0.0", "@progress/kendo-angular-dropdowns": "^5.3.0", "@progress/kendo-angular-excel-export": "^4.0.0", "@progress/kendo-angular-grid": "^5.3.0", "@progress/kendo-angular-inputs": "^7.3.1", "@progress/kendo-angular-intl": "^3.1.0", "@progress/kendo-angular-l10n": "^3.0.2", "@progress/kendo-angular-label": "^3.1.0", "@progress/kendo-angular-pdf-export": "^3.0.0", "@progress/kendo-angular-popup": "^4.0.0", "@progress/kendo-angular-treeview": "^5.4.0", "@progress/kendo-data-query": "^1.5.5", "@progress/kendo-drawing": "^1.10.1", "@progress/kendo-licensing": "^1.2.0", "@sbzen/ng-cron": "^1.0.5", "@syncfusion/ej2-angular-base": "^19.2.48", "@syncfusion/ej2-angular-buttons": "^19.2.47", "@syncfusion/ej2-angular-calendars": "^19.3.55", "@syncfusion/ej2-angular-dropdowns": "^19.2.48", "@syncfusion/ej2-angular-inputs": "^19.2.48", "@syncfusion/ej2-angular-popups": "^19.2.48", "@syncfusion/ej2-angular-schedule": "^19.2.47", "@syncfusion/ej2-angular-splitbuttons": "^19.2.44", "@syncfusion/ej2-base": "^19.2.48", "@syncfusion/ej2-buttons": "^19.2.47", "@syncfusion/ej2-dropdowns": "^19.2.48", "@syncfusion/ej2-inputs": "^19.2.48", "@syncfusion/ej2-pdfviewer": "^19.2.48", "@syncfusion/ej2-popups": "^19.2.48", "@syncfusion/ej2-react-calendars": "^19.3.46", "@webcomponents/webcomponentsjs": "^2.5.0", "@zxing/browser": "0.0.7", "@zxing/library": "0.18.6", "@zxing/ngx-scanner": "3.1.3", "ag-grid-angular": "^27.3.0", "ag-grid-community": "^27.3.0", "ag-grid-enterprise": "^27.3.0", "angular-calendar": "^0.28.28", "angular-cron-gen": "^1.0.1", "angular-froala-wysiwyg": "^3.2.6-1", "angular2-signaturepad": "^3.0.4", "angular2-uuid": "^1.1.1", "angularx-qrcode": "^11.0.0", "blg-akaun-ng-lib": "^2.0.10", "blg-akaun-ts-lib": "^0.2.************", "bootstrap": "^4.6.0", "chart.js": "^2.9.3", "concat": "^1.0.3", "core-js": "^2.6.11", "crypto-ts": "^1.0.2", "date-fns": "^2.25.0", "dexie": "^3.2.1", "dexie-observable": "^3.0.0-beta.9", "dexie-syncable": "^3.0.0-beta.9", "document-register-element": "^1.14.10", "docxtemplater": "^3.22.5", "exceljs": "^4.3.0", "express": "^4.17.1", "file-saver": "^2.0.5", "font-awesome": "^4.7.0", "froala-editor": "^4.0.7", "fs-extra": "^10.1.0", "gsap": "^3.7.1", "hammerjs": "^2.0.8", "inv": "0.0.1", "jquery": "^3.4.1", "konva": "^7.1.4", "libphonenumber-js": "^1.10.12", "mat-currency-format": "0.0.7", "moment": "^2.29.1", "moment-timezone": "^0.5.33", "ng-connection-service": "^9.0.3", "ng-mat-select-infinite-scroll": "^2.1.1", "ng-multiselect-dropdown": "^0.3.4", "ng2-canvas-whiteboard": "^4.0.2", "ng2-charts": "^2.2.3", "ng2-img-max": "^2.2.4", "ng2-pdf-viewer": "^7.0.1", "ng2-validation": "^4.2.0", "ngforage": "^6.0.0", "ngx-cron-editor": "~0.6.8", "ngx-extended-pdf-viewer": "^9.0.5", "ngx-mat-intl-tel-input": "^3.3.0", "ngx-mat-select-search": "^3.3.0", "ngx-papaparse": "^5.1.0", "ngx-perfect-scrollbar": "^10.1.0", "ngx-toastr": "^13.2.1", "ngx-treeview": "^10.0.2", "ngx-ui-loader": "^10.0.0", "ngx-websocket": "^0.4.2", "pdf-lib": "^1.17.1", "pdfmake": "^0.2.8", "perfect-scrollbar": "^1.5.2", "pizzip": "^3.1.1", "primeicons": "^7.0.0", "primeng": "^11.4.5", "quagga": "^0.12.1", "rrule": "^2.7.2", "rxjs": "^6.6.6", "rxjs-compat": "^6.6.6", "subsink": "^1.0.2", "subsink2": "^1.0.5", "swiper": "^6.8.1", "typescript-optional": "^2.0.1", "xlsx": "^0.14.5", "zone.js": "^0.11.4"}, "resolutions": {"@babel/preset-env": "^7.8.7"}, "devDependencies": {"@angular-builders/custom-webpack": "^11.1.1", "@angular-devkit/build-angular": "~0.1102.14", "@angular/cdk": "^11.2.13", "@angular/cli": "^11.2.17", "@angular/compiler-cli": "^11.2.14", "@angular/language-service": "^11.2.14", "@angularclass/hmr": "^2.1.3", "@babel/compat-data": "~7.8.0", "@babel/core": "^7.9.0", "@ngrx/store": "^11.1.1", "@schematics/angular": "11.2.14", "@types/jasmine": "~3.6.0", "@types/moment-timezone": "^0.5.30", "@types/node": "18.11.19", "@types/uuid": "^8.3.4", "angular-ide": "^0.9.74", "aws-amplify": "^2.2.7", "codelyzer": "^6.0.0", "concat": "^1.0.3", "i": "^0.3.6", "ip": "^1.1.5", "jasmine-core": "~3.6.0", "jasmine-spec-reporter": "~5.0.0", "karma": "~6.3.4", "karma-chrome-launcher": "~3.1.0", "karma-cli": "~1.0.1", "karma-coverage-istanbul-reporter": "~3.0.2", "karma-jasmine": "~4.0.0", "karma-jasmine-html-reporter": "^1.5.0", "ng-packagr": "^11.2.4", "protractor": "~7.0.0", "ts-node": "~6.0.5", "tslint": "~6.1.0", "typescript": "4.0.8"}}