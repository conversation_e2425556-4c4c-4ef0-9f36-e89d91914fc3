!function(e,t){"use strict";"object"==typeof module&&"object"==typeof module.exports?module.exports=e.document?t(e,!0):function(e){if(!e.document)throw new Error("jQuery requires a window with a document");return t(e)}:t(e)}("undefined"!=typeof window?window:this,function(e,t){"use strict";var n=[],i=Object.getPrototypeOf,r=n.slice,o=n.flat?function(e){return n.flat.call(e)}:function(e){return n.concat.apply([],e)},a=n.push,s=n.indexOf,l={},u=l.toString,c=l.hasOwnProperty,f=c.toString,d=f.call(Object),h={},p=function(e){return"function"==typeof e&&"number"!=typeof e.nodeType&&"function"!=typeof e.item},g=function(e){return null!=e&&e===e.window},m=e.document,v={type:!0,src:!0,nonce:!0,noModule:!0};function y(e,t,n){var i,r,o=(n=n||m).createElement("script");if(o.text=e,t)for(i in v)(r=t[i]||t.getAttribute&&t.getAttribute(i))&&o.setAttribute(i,r);n.head.appendChild(o).parentNode.removeChild(o)}function _(e){return null==e?e+"":"object"==typeof e||"function"==typeof e?l[u.call(e)]||"object":typeof e}var b="3.7.1",w=/HTML$/i,T=function(e,t){return new T.fn.init(e,t)};function E(e){var t=!!e&&"length"in e&&e.length,n=_(e);return!p(e)&&!g(e)&&("array"===n||0===t||"number"==typeof t&&0<t&&t-1 in e)}function x(e,t){return e.nodeName&&e.nodeName.toLowerCase()===t.toLowerCase()}T.fn=T.prototype={jquery:b,constructor:T,length:0,toArray:function(){return r.call(this)},get:function(e){return null==e?r.call(this):e<0?this[e+this.length]:this[e]},pushStack:function(e){var t=T.merge(this.constructor(),e);return t.prevObject=this,t},each:function(e){return T.each(this,e)},map:function(e){return this.pushStack(T.map(this,function(t,n){return e.call(t,n,t)}))},slice:function(){return this.pushStack(r.apply(this,arguments))},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},even:function(){return this.pushStack(T.grep(this,function(e,t){return(t+1)%2}))},odd:function(){return this.pushStack(T.grep(this,function(e,t){return t%2}))},eq:function(e){var t=this.length,n=+e+(e<0?t:0);return this.pushStack(0<=n&&n<t?[this[n]]:[])},end:function(){return this.prevObject||this.constructor()},push:a,sort:n.sort,splice:n.splice},T.extend=T.fn.extend=function(){var e,t,n,i,r,o,a=arguments[0]||{},s=1,l=arguments.length,u=!1;for("boolean"==typeof a&&(u=a,a=arguments[s]||{},s++),"object"==typeof a||p(a)||(a={}),s===l&&(a=this,s--);s<l;s++)if(null!=(e=arguments[s]))for(t in e)i=e[t],"__proto__"!==t&&a!==i&&(u&&i&&(T.isPlainObject(i)||(r=Array.isArray(i)))?(n=a[t],o=r&&!Array.isArray(n)?[]:r||T.isPlainObject(n)?n:{},r=!1,a[t]=T.extend(u,o,i)):void 0!==i&&(a[t]=i));return a},T.extend({expando:"jQuery"+(b+Math.random()).replace(/\D/g,""),isReady:!0,error:function(e){throw new Error(e)},noop:function(){},isPlainObject:function(e){var t,n;return!(!e||"[object Object]"!==u.call(e)||(t=i(e))&&("function"!=typeof(n=c.call(t,"constructor")&&t.constructor)||f.call(n)!==d))},isEmptyObject:function(e){var t;for(t in e)return!1;return!0},globalEval:function(e,t,n){y(e,{nonce:t&&t.nonce},n)},each:function(e,t){var n,i=0;if(E(e))for(n=e.length;i<n&&!1!==t.call(e[i],i,e[i]);i++);else for(i in e)if(!1===t.call(e[i],i,e[i]))break;return e},text:function(e){var t,n="",i=0,r=e.nodeType;if(!r)for(;t=e[i++];)n+=T.text(t);return 1===r||11===r?e.textContent:9===r?e.documentElement.textContent:3===r||4===r?e.nodeValue:n},makeArray:function(e,t){var n=t||[];return null!=e&&(E(Object(e))?T.merge(n,"string"==typeof e?[e]:e):a.call(n,e)),n},inArray:function(e,t,n){return null==t?-1:s.call(t,e,n)},isXMLDoc:function(e){var t=e&&(e.ownerDocument||e).documentElement;return!w.test(e&&e.namespaceURI||t&&t.nodeName||"HTML")},merge:function(e,t){for(var n=+t.length,i=0,r=e.length;i<n;i++)e[r++]=t[i];return e.length=r,e},grep:function(e,t,n){for(var i=[],r=0,o=e.length,a=!n;r<o;r++)!t(e[r],r)!==a&&i.push(e[r]);return i},map:function(e,t,n){var i,r,a=0,s=[];if(E(e))for(i=e.length;a<i;a++)null!=(r=t(e[a],a,n))&&s.push(r);else for(a in e)null!=(r=t(e[a],a,n))&&s.push(r);return o(s)},guid:1,support:h}),"function"==typeof Symbol&&(T.fn[Symbol.iterator]=n[Symbol.iterator]),T.each("Boolean Number String Function Array Date RegExp Object Error Symbol".split(" "),function(e,t){l["[object "+t+"]"]=t.toLowerCase()});var C=n.pop,S=n.sort,k=n.splice,A="[\\x20\\t\\r\\n\\f]",D=new RegExp("^"+A+"+|((?:^|[^\\\\])(?:\\\\.)*)"+A+"+$","g");T.contains=function(e,t){var n=t&&t.parentNode;return e===n||!(!n||1!==n.nodeType||!(e.contains?e.contains(n):e.compareDocumentPosition&&16&e.compareDocumentPosition(n)))};var N=/([\0-\x1f\x7f]|^-?\d)|^-$|[^\x80-\uFFFF\w-]/g;function j(e,t){return t?"\0"===e?"\ufffd":e.slice(0,-1)+"\\"+e.charCodeAt(e.length-1).toString(16)+" ":"\\"+e}T.escapeSelector=function(e){return(e+"").replace(N,j)};var I=m,O=a;!function(){var t,i,o,a,l,u,f,d,p,g,m=O,v=T.expando,y=0,_=0,b=ee(),w=ee(),E=ee(),N=ee(),j=function(e,t){return e===t&&(l=!0),0},L="checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped",q="(?:\\\\[\\da-fA-F]{1,6}"+A+"?|\\\\[^\\r\\n\\f]|[\\w-]|[^\0-\\x7f])+",P="\\["+A+"*("+q+")(?:"+A+"*([*^$|!~]?=)"+A+"*(?:'((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\"|("+q+"))|)"+A+"*\\]",R=":("+q+")(?:\\((('((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\")|((?:\\\\.|[^\\\\()[\\]]|"+P+")*)|.*)\\)|)",H=new RegExp(A+"+","g"),F=new RegExp("^"+A+"*,"+A+"*"),M=new RegExp("^"+A+"*([>+~]|"+A+")"+A+"*"),B=new RegExp(A+"|>"),W=new RegExp(R),U=new RegExp("^"+q+"$"),Q={ID:new RegExp("^#("+q+")"),CLASS:new RegExp("^\\.("+q+")"),TAG:new RegExp("^("+q+"|[*])"),ATTR:new RegExp("^"+P),PSEUDO:new RegExp("^"+R),CHILD:new RegExp("^:(only|first|last|nth|nth-last)-(child|of-type)(?:\\("+A+"*(even|odd|(([+-]|)(\\d*)n|)"+A+"*(?:([+-]|)"+A+"*(\\d+)|))"+A+"*\\)|)","i"),bool:new RegExp("^(?:"+L+")$","i"),needsContext:new RegExp("^"+A+"*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\\("+A+"*((?:-\\d)?\\d*)"+A+"*\\)|)(?=[^-]|$)","i")},$=/^(?:input|select|textarea|button)$/i,z=/^h\d$/i,X=/^(?:#([\w-]+)|(\w+)|\.([\w-]+))$/,V=/[+~]/,Y=new RegExp("\\\\[\\da-fA-F]{1,6}"+A+"?|\\\\([^\\r\\n\\f])","g"),K=function(e,t){var n="0x"+e.slice(1)-65536;return t||(n<0?String.fromCharCode(n+65536):String.fromCharCode(n>>10|55296,1023&n|56320))},G=function(){le()},J=de(function(e){return!0===e.disabled&&x(e,"fieldset")},{dir:"parentNode",next:"legend"});try{m.apply(n=r.call(I.childNodes),I.childNodes)}catch(t){m={apply:function(e,t){O.apply(e,r.call(t))},call:function(e){O.apply(e,r.call(arguments,1))}}}function Z(e,t,n,i){var r,o,a,s,l,c,f,g=t&&t.ownerDocument,y=t?t.nodeType:9;if(n=n||[],"string"!=typeof e||!e||1!==y&&9!==y&&11!==y)return n;if(!i&&(le(t),t=t||u,d)){if(11!==y&&(l=X.exec(e)))if(r=l[1]){if(9===y){if(!(a=t.getElementById(r)))return n;if(a.id===r)return m.call(n,a),n}else if(g&&(a=g.getElementById(r))&&Z.contains(t,a)&&a.id===r)return m.call(n,a),n}else{if(l[2])return m.apply(n,t.getElementsByTagName(e)),n;if((r=l[3])&&t.getElementsByClassName)return m.apply(n,t.getElementsByClassName(r)),n}if(!(N[e+" "]||p&&p.test(e))){if(f=e,g=t,1===y&&(B.test(e)||M.test(e))){for((g=V.test(e)&&se(t.parentNode)||t)==t&&h.scope||((s=t.getAttribute("id"))?s=T.escapeSelector(s):t.setAttribute("id",s=v)),o=(c=ce(e)).length;o--;)c[o]=(s?"#"+s:":scope")+" "+fe(c[o]);f=c.join(",")}try{return m.apply(n,g.querySelectorAll(f)),n}catch(t){N(e,!0)}finally{s===v&&t.removeAttribute("id")}}}return ye(e.replace(D,"$1"),t,n,i)}function ee(){var e=[];return function t(n,r){return e.push(n+" ")>i.cacheLength&&delete t[e.shift()],t[n+" "]=r}}function te(e){return e[v]=!0,e}function ne(e){var t=u.createElement("fieldset");try{return!!e(t)}catch(e){return!1}finally{t.parentNode&&t.parentNode.removeChild(t),t=null}}function ie(e){return function(t){return x(t,"input")&&t.type===e}}function re(e){return function(t){return(x(t,"input")||x(t,"button"))&&t.type===e}}function oe(e){return function(t){return"form"in t?t.parentNode&&!1===t.disabled?"label"in t?"label"in t.parentNode?t.parentNode.disabled===e:t.disabled===e:t.isDisabled===e||t.isDisabled!==!e&&J(t)===e:t.disabled===e:"label"in t&&t.disabled===e}}function ae(e){return te(function(t){return t=+t,te(function(n,i){for(var r,o=e([],n.length,t),a=o.length;a--;)n[r=o[a]]&&(n[r]=!(i[r]=n[r]))})})}function se(e){return e&&void 0!==e.getElementsByTagName&&e}function le(e){var t,n=e?e.ownerDocument||e:I;return n!=u&&9===n.nodeType&&n.documentElement&&(f=(u=n).documentElement,d=!T.isXMLDoc(u),g=f.matches||f.webkitMatchesSelector||f.msMatchesSelector,f.msMatchesSelector&&I!=u&&(t=u.defaultView)&&t.top!==t&&t.addEventListener("unload",G),h.getById=ne(function(e){return f.appendChild(e).id=T.expando,!u.getElementsByName||!u.getElementsByName(T.expando).length}),h.disconnectedMatch=ne(function(e){return g.call(e,"*")}),h.scope=ne(function(){return u.querySelectorAll(":scope")}),h.cssHas=ne(function(){try{return u.querySelector(":has(*,:jqfake)"),!1}catch(e){return!0}}),h.getById?(i.filter.ID=function(e){var t=e.replace(Y,K);return function(e){return e.getAttribute("id")===t}},i.find.ID=function(e,t){if(void 0!==t.getElementById&&d){var n=t.getElementById(e);return n?[n]:[]}}):(i.filter.ID=function(e){var t=e.replace(Y,K);return function(e){var n=void 0!==e.getAttributeNode&&e.getAttributeNode("id");return n&&n.value===t}},i.find.ID=function(e,t){if(void 0!==t.getElementById&&d){var n,i,r,o=t.getElementById(e);if(o){if((n=o.getAttributeNode("id"))&&n.value===e)return[o];for(r=t.getElementsByName(e),i=0;o=r[i++];)if((n=o.getAttributeNode("id"))&&n.value===e)return[o]}return[]}}),i.find.TAG=function(e,t){return void 0!==t.getElementsByTagName?t.getElementsByTagName(e):t.querySelectorAll(e)},i.find.CLASS=function(e,t){if(void 0!==t.getElementsByClassName&&d)return t.getElementsByClassName(e)},p=[],ne(function(e){var t;f.appendChild(e).innerHTML="<a id='"+v+"' href='' disabled='disabled'></a><select id='"+v+"-\r\\' disabled='disabled'><option selected=''></option></select>",e.querySelectorAll("[selected]").length||p.push("\\["+A+"*(?:value|"+L+")"),e.querySelectorAll("[id~="+v+"-]").length||p.push("~="),e.querySelectorAll("a#"+v+"+*").length||p.push(".#.+[+~]"),e.querySelectorAll(":checked").length||p.push(":checked"),(t=u.createElement("input")).setAttribute("type","hidden"),e.appendChild(t).setAttribute("name","D"),f.appendChild(e).disabled=!0,2!==e.querySelectorAll(":disabled").length&&p.push(":enabled",":disabled"),(t=u.createElement("input")).setAttribute("name",""),e.appendChild(t),e.querySelectorAll("[name='']").length||p.push("\\["+A+"*name"+A+"*="+A+"*(?:''|\"\")")}),h.cssHas||p.push(":has"),p=p.length&&new RegExp(p.join("|")),j=function(e,t){if(e===t)return l=!0,0;var n=!e.compareDocumentPosition-!t.compareDocumentPosition;return n||(1&(n=(e.ownerDocument||e)==(t.ownerDocument||t)?e.compareDocumentPosition(t):1)||!h.sortDetached&&t.compareDocumentPosition(e)===n?e===u||e.ownerDocument==I&&Z.contains(I,e)?-1:t===u||t.ownerDocument==I&&Z.contains(I,t)?1:a?s.call(a,e)-s.call(a,t):0:4&n?-1:1)}),u}for(t in Z.matches=function(e,t){return Z(e,null,null,t)},Z.matchesSelector=function(e,t){if(le(e),d&&!N[t+" "]&&(!p||!p.test(t)))try{var n=g.call(e,t);if(n||h.disconnectedMatch||e.document&&11!==e.document.nodeType)return n}catch(e){N(t,!0)}return 0<Z(t,u,null,[e]).length},Z.contains=function(e,t){return(e.ownerDocument||e)!=u&&le(e),T.contains(e,t)},Z.attr=function(e,t){(e.ownerDocument||e)!=u&&le(e);var n=i.attrHandle[t.toLowerCase()],r=n&&c.call(i.attrHandle,t.toLowerCase())?n(e,t,!d):void 0;return void 0!==r?r:e.getAttribute(t)},Z.error=function(e){throw new Error("Syntax error, unrecognized expression: "+e)},T.uniqueSort=function(e){var t,n=[],i=0,o=0;if(l=!h.sortStable,a=!h.sortStable&&r.call(e,0),S.call(e,j),l){for(;t=e[o++];)t===e[o]&&(i=n.push(o));for(;i--;)k.call(e,n[i],1)}return a=null,e},T.fn.uniqueSort=function(){return this.pushStack(T.uniqueSort(r.apply(this)))},(i=T.expr={cacheLength:50,createPseudo:te,match:Q,attrHandle:{},find:{},relative:{">":{dir:"parentNode",first:!0}," ":{dir:"parentNode"},"+":{dir:"previousSibling",first:!0},"~":{dir:"previousSibling"}},preFilter:{ATTR:function(e){return e[1]=e[1].replace(Y,K),e[3]=(e[3]||e[4]||e[5]||"").replace(Y,K),"~="===e[2]&&(e[3]=" "+e[3]+" "),e.slice(0,4)},CHILD:function(e){return e[1]=e[1].toLowerCase(),"nth"===e[1].slice(0,3)?(e[3]||Z.error(e[0]),e[4]=+(e[4]?e[5]+(e[6]||1):2*("even"===e[3]||"odd"===e[3])),e[5]=+(e[7]+e[8]||"odd"===e[3])):e[3]&&Z.error(e[0]),e},PSEUDO:function(e){var t,n=!e[6]&&e[2];return Q.CHILD.test(e[0])?null:(e[3]?e[2]=e[4]||e[5]||"":n&&W.test(n)&&(t=ce(n,!0))&&(t=n.indexOf(")",n.length-t)-n.length)&&(e[0]=e[0].slice(0,t),e[2]=n.slice(0,t)),e.slice(0,3))}},filter:{TAG:function(e){var t=e.replace(Y,K).toLowerCase();return"*"===e?function(){return!0}:function(e){return x(e,t)}},CLASS:function(e){var t=b[e+" "];return t||(t=new RegExp("(^|"+A+")"+e+"("+A+"|$)"))&&b(e,function(e){return t.test("string"==typeof e.className&&e.className||void 0!==e.getAttribute&&e.getAttribute("class")||"")})},ATTR:function(e,t,n){return function(i){var r=Z.attr(i,e);return null==r?"!="===t:!t||(r+="","="===t?r===n:"!="===t?r!==n:"^="===t?n&&0===r.indexOf(n):"*="===t?n&&-1<r.indexOf(n):"$="===t?n&&r.slice(-n.length)===n:"~="===t?-1<(" "+r.replace(H," ")+" ").indexOf(n):"|="===t&&(r===n||r.slice(0,n.length+1)===n+"-"))}},CHILD:function(e,t,n,i,r){var o="nth"!==e.slice(0,3),a="last"!==e.slice(-4),s="of-type"===t;return 1===i&&0===r?function(e){return!!e.parentNode}:function(t,n,l){var u,c,f,d,h,p=o!==a?"nextSibling":"previousSibling",g=t.parentNode,m=s&&t.nodeName.toLowerCase(),_=!l&&!s,b=!1;if(g){if(o){for(;p;){for(f=t;f=f[p];)if(s?x(f,m):1===f.nodeType)return!1;h=p="only"===e&&!h&&"nextSibling"}return!0}if(h=[a?g.firstChild:g.lastChild],a&&_){for(b=(d=(u=(c=g[v]||(g[v]={}))[e]||[])[0]===y&&u[1])&&u[2],f=d&&g.childNodes[d];f=++d&&f&&f[p]||(b=d=0)||h.pop();)if(1===f.nodeType&&++b&&f===t){c[e]=[y,d,b];break}}else if(_&&(b=d=(u=(c=t[v]||(t[v]={}))[e]||[])[0]===y&&u[1]),!1===b)for(;(f=++d&&f&&f[p]||(b=d=0)||h.pop())&&(!(s?x(f,m):1===f.nodeType)||!++b||(_&&((c=f[v]||(f[v]={}))[e]=[y,b]),f!==t)););return(b-=r)===i||b%i==0&&0<=b/i}}},PSEUDO:function(e,t){var n,r=i.pseudos[e]||i.setFilters[e.toLowerCase()]||Z.error("unsupported pseudo: "+e);return r[v]?r(t):1<r.length?(n=[e,e,"",t],i.setFilters.hasOwnProperty(e.toLowerCase())?te(function(e,n){for(var i,o=r(e,t),a=o.length;a--;)e[i=s.call(e,o[a])]=!(n[i]=o[a])}):function(e){return r(e,0,n)}):r}},pseudos:{not:te(function(e){var t=[],n=[],i=ve(e.replace(D,"$1"));return i[v]?te(function(e,t,n,r){for(var o,a=i(e,null,r,[]),s=e.length;s--;)(o=a[s])&&(e[s]=!(t[s]=o))}):function(e,r,o){return t[0]=e,i(t,null,o,n),t[0]=null,!n.pop()}}),has:te(function(e){return function(t){return 0<Z(e,t).length}}),contains:te(function(e){return e=e.replace(Y,K),function(t){return-1<(t.textContent||T.text(t)).indexOf(e)}}),lang:te(function(e){return U.test(e||"")||Z.error("unsupported lang: "+e),e=e.replace(Y,K).toLowerCase(),function(t){var n;do{if(n=d?t.lang:t.getAttribute("xml:lang")||t.getAttribute("lang"))return(n=n.toLowerCase())===e||0===n.indexOf(e+"-")}while((t=t.parentNode)&&1===t.nodeType);return!1}}),target:function(t){var n=e.location&&e.location.hash;return n&&n.slice(1)===t.id},root:function(e){return e===f},focus:function(e){return e===function(){try{return u.activeElement}catch(e){}}()&&u.hasFocus()&&!!(e.type||e.href||~e.tabIndex)},enabled:oe(!1),disabled:oe(!0),checked:function(e){return x(e,"input")&&!!e.checked||x(e,"option")&&!!e.selected},selected:function(e){return!0===e.selected},empty:function(e){for(e=e.firstChild;e;e=e.nextSibling)if(e.nodeType<6)return!1;return!0},parent:function(e){return!i.pseudos.empty(e)},header:function(e){return z.test(e.nodeName)},input:function(e){return $.test(e.nodeName)},button:function(e){return x(e,"input")&&"button"===e.type||x(e,"button")},text:function(e){var t;return x(e,"input")&&"text"===e.type&&(null==(t=e.getAttribute("type"))||"text"===t.toLowerCase())},first:ae(function(){return[0]}),last:ae(function(e,t){return[t-1]}),eq:ae(function(e,t,n){return[n<0?n+t:n]}),even:ae(function(e,t){for(var n=0;n<t;n+=2)e.push(n);return e}),odd:ae(function(e,t){for(var n=1;n<t;n+=2)e.push(n);return e}),lt:ae(function(e,t,n){var i;for(i=n<0?n+t:t<n?t:n;0<=--i;)e.push(i);return e}),gt:ae(function(e,t,n){for(var i=n<0?n+t:n;++i<t;)e.push(i);return e})}}).pseudos.nth=i.pseudos.eq,{radio:!0,checkbox:!0,file:!0,password:!0,image:!0})i.pseudos[t]=ie(t);for(t in{submit:!0,reset:!0})i.pseudos[t]=re(t);function ue(){}function ce(e,t){var n,r,o,a,s,l,u,c=w[e+" "];if(c)return t?0:c.slice(0);for(s=e,l=[],u=i.preFilter;s;){for(a in n&&!(r=F.exec(s))||(r&&(s=s.slice(r[0].length)||s),l.push(o=[])),n=!1,(r=M.exec(s))&&(n=r.shift(),o.push({value:n,type:r[0].replace(D," ")}),s=s.slice(n.length)),i.filter)!(r=Q[a].exec(s))||u[a]&&!(r=u[a](r))||(n=r.shift(),o.push({value:n,type:a,matches:r}),s=s.slice(n.length));if(!n)break}return t?s.length:s?Z.error(e):w(e,l).slice(0)}function fe(e){for(var t=0,n=e.length,i="";t<n;t++)i+=e[t].value;return i}function de(e,t,n){var i=t.dir,r=t.next,o=r||i,a=n&&"parentNode"===o,s=_++;return t.first?function(t,n,r){for(;t=t[i];)if(1===t.nodeType||a)return e(t,n,r);return!1}:function(t,n,l){var u,c,f=[y,s];if(l){for(;t=t[i];)if((1===t.nodeType||a)&&e(t,n,l))return!0}else for(;t=t[i];)if(1===t.nodeType||a)if(c=t[v]||(t[v]={}),r&&x(t,r))t=t[i]||t;else{if((u=c[o])&&u[0]===y&&u[1]===s)return f[2]=u[2];if((c[o]=f)[2]=e(t,n,l))return!0}return!1}}function he(e){return 1<e.length?function(t,n,i){for(var r=e.length;r--;)if(!e[r](t,n,i))return!1;return!0}:e[0]}function pe(e,t,n,i,r){for(var o,a=[],s=0,l=e.length,u=null!=t;s<l;s++)(o=e[s])&&(n&&!n(o,i,r)||(a.push(o),u&&t.push(s)));return a}function ge(e,t,n,i,r,o){return i&&!i[v]&&(i=ge(i)),r&&!r[v]&&(r=ge(r,o)),te(function(o,a,l,u){var c,f,d,h,p=[],g=[],v=a.length,y=o||function(e,t,n){for(var i=0,r=t.length;i<r;i++)Z(e,t[i],n);return n}(t||"*",l.nodeType?[l]:l,[]),_=!e||!o&&t?y:pe(y,p,e,l,u);if(n?n(_,h=r||(o?e:v||i)?[]:a,l,u):h=_,i)for(c=pe(h,g),i(c,[],l,u),f=c.length;f--;)(d=c[f])&&(h[g[f]]=!(_[g[f]]=d));if(o){if(r||e){if(r){for(c=[],f=h.length;f--;)(d=h[f])&&c.push(_[f]=d);r(null,h=[],c,u)}for(f=h.length;f--;)(d=h[f])&&-1<(c=r?s.call(o,d):p[f])&&(o[c]=!(a[c]=d))}}else h=pe(h===a?h.splice(v,h.length):h),r?r(null,a,h,u):m.apply(a,h)})}function me(e){for(var t,n,r,a=e.length,l=i.relative[e[0].type],u=l||i.relative[" "],c=l?1:0,f=de(function(e){return e===t},u,!0),d=de(function(e){return-1<s.call(t,e)},u,!0),h=[function(e,n,i){var r=!l&&(i||n!=o)||((t=n).nodeType?f(e,n,i):d(e,n,i));return t=null,r}];c<a;c++)if(n=i.relative[e[c].type])h=[de(he(h),n)];else{if((n=i.filter[e[c].type].apply(null,e[c].matches))[v]){for(r=++c;r<a&&!i.relative[e[r].type];r++);return ge(1<c&&he(h),1<c&&fe(e.slice(0,c-1).concat({value:" "===e[c-2].type?"*":""})).replace(D,"$1"),n,c<r&&me(e.slice(c,r)),r<a&&me(e=e.slice(r)),r<a&&fe(e))}h.push(n)}return he(h)}function ve(e,t){var n,r,a,s,l,c,f=[],h=[],p=E[e+" "];if(!p){for(t||(t=ce(e)),n=t.length;n--;)(p=me(t[n]))[v]?f.push(p):h.push(p);(p=E(e,(r=h,s=0<(a=f).length,l=0<r.length,c=function(e,t,n,c,f){var h,p,g,v=0,_="0",b=e&&[],w=[],E=o,x=e||l&&i.find.TAG("*",f),S=y+=null==E?1:Math.random()||.1,k=x.length;for(f&&(o=t==u||t||f);_!==k&&null!=(h=x[_]);_++){if(l&&h){for(p=0,t||h.ownerDocument==u||(le(h),n=!d);g=r[p++];)if(g(h,t||u,n)){m.call(c,h);break}f&&(y=S)}s&&((h=!g&&h)&&v--,e&&b.push(h))}if(v+=_,s&&_!==v){for(p=0;g=a[p++];)g(b,w,t,n);if(e){if(0<v)for(;_--;)b[_]||w[_]||(w[_]=C.call(c));w=pe(w)}m.apply(c,w),f&&!e&&0<w.length&&1<v+a.length&&T.uniqueSort(c)}return f&&(y=S,o=E),b},s?te(c):c))).selector=e}return p}function ye(e,t,n,r){var o,a,s,l,u,c="function"==typeof e&&e,f=!r&&ce(e=c.selector||e);if(n=n||[],1===f.length){if(2<(a=f[0]=f[0].slice(0)).length&&"ID"===(s=a[0]).type&&9===t.nodeType&&d&&i.relative[a[1].type]){if(!(t=(i.find.ID(s.matches[0].replace(Y,K),t)||[])[0]))return n;c&&(t=t.parentNode),e=e.slice(a.shift().value.length)}for(o=Q.needsContext.test(e)?0:a.length;o--&&!i.relative[l=(s=a[o]).type];)if((u=i.find[l])&&(r=u(s.matches[0].replace(Y,K),V.test(a[0].type)&&se(t.parentNode)||t))){if(a.splice(o,1),!(e=r.length&&fe(a)))return m.apply(n,r),n;break}}return(c||ve(e,f))(r,t,!d,n,!t||V.test(e)&&se(t.parentNode)||t),n}ue.prototype=i.filters=i.pseudos,i.setFilters=new ue,h.sortStable=v.split("").sort(j).join("")===v,le(),h.sortDetached=ne(function(e){return 1&e.compareDocumentPosition(u.createElement("fieldset"))}),T.find=Z,T.expr[":"]=T.expr.pseudos,T.unique=T.uniqueSort,Z.compile=ve,Z.select=ye,Z.setDocument=le,Z.tokenize=ce,Z.escape=T.escapeSelector,Z.getText=T.text,Z.isXML=T.isXMLDoc,Z.selectors=T.expr,Z.support=T.support,Z.uniqueSort=T.uniqueSort}();var L=function(e,t,n){for(var i=[],r=void 0!==n;(e=e[t])&&9!==e.nodeType;)if(1===e.nodeType){if(r&&T(e).is(n))break;i.push(e)}return i},q=function(e,t){for(var n=[];e;e=e.nextSibling)1===e.nodeType&&e!==t&&n.push(e);return n},P=T.expr.match.needsContext,R=/^<([a-z][^\/\0>:\x20\t\r\n\f]*)[\x20\t\r\n\f]*\/?>(?:<\/\1>|)$/i;function H(e,t,n){return p(t)?T.grep(e,function(e,i){return!!t.call(e,i,e)!==n}):t.nodeType?T.grep(e,function(e){return e===t!==n}):"string"!=typeof t?T.grep(e,function(e){return-1<s.call(t,e)!==n}):T.filter(t,e,n)}T.filter=function(e,t,n){var i=t[0];return n&&(e=":not("+e+")"),1===t.length&&1===i.nodeType?T.find.matchesSelector(i,e)?[i]:[]:T.find.matches(e,T.grep(t,function(e){return 1===e.nodeType}))},T.fn.extend({find:function(e){var t,n,i=this.length,r=this;if("string"!=typeof e)return this.pushStack(T(e).filter(function(){for(t=0;t<i;t++)if(T.contains(r[t],this))return!0}));for(n=this.pushStack([]),t=0;t<i;t++)T.find(e,r[t],n);return 1<i?T.uniqueSort(n):n},filter:function(e){return this.pushStack(H(this,e||[],!1))},not:function(e){return this.pushStack(H(this,e||[],!0))},is:function(e){return!!H(this,"string"==typeof e&&P.test(e)?T(e):e||[],!1).length}});var F,M=/^(?:\s*(<[\w\W]+>)[^>]*|#([\w-]+))$/;(T.fn.init=function(e,t,n){var i,r;if(!e)return this;if(n=n||F,"string"==typeof e){if(!(i="<"===e[0]&&">"===e[e.length-1]&&3<=e.length?[null,e,null]:M.exec(e))||!i[1]&&t)return!t||t.jquery?(t||n).find(e):this.constructor(t).find(e);if(i[1]){if(T.merge(this,T.parseHTML(i[1],(t=t instanceof T?t[0]:t)&&t.nodeType?t.ownerDocument||t:m,!0)),R.test(i[1])&&T.isPlainObject(t))for(i in t)p(this[i])?this[i](t[i]):this.attr(i,t[i]);return this}return(r=m.getElementById(i[2]))&&(this[0]=r,this.length=1),this}return e.nodeType?(this[0]=e,this.length=1,this):p(e)?void 0!==n.ready?n.ready(e):e(T):T.makeArray(e,this)}).prototype=T.fn,F=T(m);var B=/^(?:parents|prev(?:Until|All))/,W={children:!0,contents:!0,next:!0,prev:!0};function U(e,t){for(;(e=e[t])&&1!==e.nodeType;);return e}T.fn.extend({has:function(e){var t=T(e,this),n=t.length;return this.filter(function(){for(var e=0;e<n;e++)if(T.contains(this,t[e]))return!0})},closest:function(e,t){var n,i=0,r=this.length,o=[],a="string"!=typeof e&&T(e);if(!P.test(e))for(;i<r;i++)for(n=this[i];n&&n!==t;n=n.parentNode)if(n.nodeType<11&&(a?-1<a.index(n):1===n.nodeType&&T.find.matchesSelector(n,e))){o.push(n);break}return this.pushStack(1<o.length?T.uniqueSort(o):o)},index:function(e){return e?"string"==typeof e?s.call(T(e),this[0]):s.call(this,e.jquery?e[0]:e):this[0]&&this[0].parentNode?this.first().prevAll().length:-1},add:function(e,t){return this.pushStack(T.uniqueSort(T.merge(this.get(),T(e,t))))},addBack:function(e){return this.add(null==e?this.prevObject:this.prevObject.filter(e))}}),T.each({parent:function(e){var t=e.parentNode;return t&&11!==t.nodeType?t:null},parents:function(e){return L(e,"parentNode")},parentsUntil:function(e,t,n){return L(e,"parentNode",n)},next:function(e){return U(e,"nextSibling")},prev:function(e){return U(e,"previousSibling")},nextAll:function(e){return L(e,"nextSibling")},prevAll:function(e){return L(e,"previousSibling")},nextUntil:function(e,t,n){return L(e,"nextSibling",n)},prevUntil:function(e,t,n){return L(e,"previousSibling",n)},siblings:function(e){return q((e.parentNode||{}).firstChild,e)},children:function(e){return q(e.firstChild)},contents:function(e){return null!=e.contentDocument&&i(e.contentDocument)?e.contentDocument:(x(e,"template")&&(e=e.content||e),T.merge([],e.childNodes))}},function(e,t){T.fn[e]=function(n,i){var r=T.map(this,t,n);return"Until"!==e.slice(-5)&&(i=n),i&&"string"==typeof i&&(r=T.filter(i,r)),1<this.length&&(W[e]||T.uniqueSort(r),B.test(e)&&r.reverse()),this.pushStack(r)}});var Q=/[^\x20\t\r\n\f]+/g;function $(e){return e}function z(e){throw e}function X(e,t,n,i){var r;try{e&&p(r=e.promise)?r.call(e).done(t).fail(n):e&&p(r=e.then)?r.call(e,t,n):t.apply(void 0,[e].slice(i))}catch(e){n.apply(void 0,[e])}}T.Callbacks=function(e){var t;e="string"==typeof e?(t={},T.each(e.match(Q)||[],function(e,n){t[n]=!0}),t):T.extend({},e);var n,i,r,o,a=[],s=[],l=-1,u=function(){for(o=o||e.once,r=n=!0;s.length;l=-1)for(i=s.shift();++l<a.length;)!1===a[l].apply(i[0],i[1])&&e.stopOnFalse&&(l=a.length,i=!1);e.memory||(i=!1),n=!1,o&&(a=i?[]:"")},c={add:function(){return a&&(i&&!n&&(l=a.length-1,s.push(i)),function t(n){T.each(n,function(n,i){p(i)?e.unique&&c.has(i)||a.push(i):i&&i.length&&"string"!==_(i)&&t(i)})}(arguments),i&&!n&&u()),this},remove:function(){return T.each(arguments,function(e,t){for(var n;-1<(n=T.inArray(t,a,n));)a.splice(n,1),n<=l&&l--}),this},has:function(e){return e?-1<T.inArray(e,a):0<a.length},empty:function(){return a&&(a=[]),this},disable:function(){return o=s=[],a=i="",this},disabled:function(){return!a},lock:function(){return o=s=[],i||n||(a=i=""),this},locked:function(){return!!o},fireWith:function(e,t){return o||(t=[e,(t=t||[]).slice?t.slice():t],s.push(t),n||u()),this},fire:function(){return c.fireWith(this,arguments),this},fired:function(){return!!r}};return c},T.extend({Deferred:function(t){var n=[["notify","progress",T.Callbacks("memory"),T.Callbacks("memory"),2],["resolve","done",T.Callbacks("once memory"),T.Callbacks("once memory"),0,"resolved"],["reject","fail",T.Callbacks("once memory"),T.Callbacks("once memory"),1,"rejected"]],i="pending",r={state:function(){return i},always:function(){return o.done(arguments).fail(arguments),this},catch:function(e){return r.then(null,e)},pipe:function(){var e=arguments;return T.Deferred(function(t){T.each(n,function(n,i){var r=p(e[i[4]])&&e[i[4]];o[i[1]](function(){var e=r&&r.apply(this,arguments);e&&p(e.promise)?e.promise().progress(t.notify).done(t.resolve).fail(t.reject):t[i[0]+"With"](this,r?[e]:arguments)})}),e=null}).promise()},then:function(t,i,r){var o=0;function a(t,n,i,r){return function(){var s=this,l=arguments,u=function(){var e,u;if(!(t<o)){if((e=i.apply(s,l))===n.promise())throw new TypeError("Thenable self-resolution");p(u=e&&("object"==typeof e||"function"==typeof e)&&e.then)?r?u.call(e,a(o,n,$,r),a(o,n,z,r)):(o++,u.call(e,a(o,n,$,r),a(o,n,z,r),a(o,n,$,n.notifyWith))):(i!==$&&(s=void 0,l=[e]),(r||n.resolveWith)(s,l))}},c=r?u:function(){try{u()}catch(u){T.Deferred.exceptionHook&&T.Deferred.exceptionHook(u,c.error),o<=t+1&&(i!==z&&(s=void 0,l=[u]),n.rejectWith(s,l))}};t?c():(T.Deferred.getErrorHook?c.error=T.Deferred.getErrorHook():T.Deferred.getStackHook&&(c.error=T.Deferred.getStackHook()),e.setTimeout(c))}}return T.Deferred(function(e){n[0][3].add(a(0,e,p(r)?r:$,e.notifyWith)),n[1][3].add(a(0,e,p(t)?t:$)),n[2][3].add(a(0,e,p(i)?i:z))}).promise()},promise:function(e){return null!=e?T.extend(e,r):r}},o={};return T.each(n,function(e,t){var a=t[2],s=t[5];r[t[1]]=a.add,s&&a.add(function(){i=s},n[3-e][2].disable,n[3-e][3].disable,n[0][2].lock,n[0][3].lock),a.add(t[3].fire),o[t[0]]=function(){return o[t[0]+"With"](this===o?void 0:this,arguments),this},o[t[0]+"With"]=a.fireWith}),r.promise(o),t&&t.call(o,o),o},when:function(e){var t=arguments.length,n=t,i=Array(n),o=r.call(arguments),a=T.Deferred(),s=function(e){return function(n){i[e]=this,o[e]=1<arguments.length?r.call(arguments):n,--t||a.resolveWith(i,o)}};if(t<=1&&(X(e,a.done(s(n)).resolve,a.reject,!t),"pending"===a.state()||p(o[n]&&o[n].then)))return a.then();for(;n--;)X(o[n],s(n),a.reject);return a.promise()}});var V=/^(Eval|Internal|Range|Reference|Syntax|Type|URI)Error$/;T.Deferred.exceptionHook=function(t,n){e.console&&e.console.warn&&t&&V.test(t.name)&&e.console.warn("jQuery.Deferred exception: "+t.message,t.stack,n)},T.readyException=function(t){e.setTimeout(function(){throw t})};var Y=T.Deferred();function K(){m.removeEventListener("DOMContentLoaded",K),e.removeEventListener("load",K),T.ready()}T.fn.ready=function(e){return Y.then(e).catch(function(e){T.readyException(e)}),this},T.extend({isReady:!1,readyWait:1,ready:function(e){(!0===e?--T.readyWait:T.isReady)||(T.isReady=!0)!==e&&0<--T.readyWait||Y.resolveWith(m,[T])}}),T.ready.then=Y.then,"complete"===m.readyState||"loading"!==m.readyState&&!m.documentElement.doScroll?e.setTimeout(T.ready):(m.addEventListener("DOMContentLoaded",K),e.addEventListener("load",K));var G=function(e,t,n,i,r,o,a){var s=0,l=e.length,u=null==n;if("object"===_(n))for(s in r=!0,n)G(e,t,s,n[s],!0,o,a);else if(void 0!==i&&(r=!0,p(i)||(a=!0),u&&(a?(t.call(e,i),t=null):(u=t,t=function(e,t,n){return u.call(T(e),n)})),t))for(;s<l;s++)t(e[s],n,a?i:i.call(e[s],s,t(e[s],n)));return r?e:u?t.call(e):l?t(e[0],n):o},J=/^-ms-/,Z=/-([a-z])/g;function ee(e,t){return t.toUpperCase()}function te(e){return e.replace(J,"ms-").replace(Z,ee)}var ne=function(e){return 1===e.nodeType||9===e.nodeType||!+e.nodeType};function ie(){this.expando=T.expando+ie.uid++}ie.uid=1,ie.prototype={cache:function(e){var t=e[this.expando];return t||(t={},ne(e)&&(e.nodeType?e[this.expando]=t:Object.defineProperty(e,this.expando,{value:t,configurable:!0}))),t},set:function(e,t,n){var i,r=this.cache(e);if("string"==typeof t)r[te(t)]=n;else for(i in t)r[te(i)]=t[i];return r},get:function(e,t){return void 0===t?this.cache(e):e[this.expando]&&e[this.expando][te(t)]},access:function(e,t,n){return void 0===t||t&&"string"==typeof t&&void 0===n?this.get(e,t):(this.set(e,t,n),void 0!==n?n:t)},remove:function(e,t){var n,i=e[this.expando];if(void 0!==i){if(void 0!==t){n=(t=Array.isArray(t)?t.map(te):(t=te(t))in i?[t]:t.match(Q)||[]).length;for(;n--;)delete i[t[n]]}(void 0===t||T.isEmptyObject(i))&&(e.nodeType?e[this.expando]=void 0:delete e[this.expando])}},hasData:function(e){var t=e[this.expando];return void 0!==t&&!T.isEmptyObject(t)}};var re=new ie,oe=new ie,ae=/^(?:\{[\w\W]*\}|\[[\w\W]*\])$/,se=/[A-Z]/g;function le(e,t,n){var i,r;if(void 0===n&&1===e.nodeType)if(i="data-"+t.replace(se,"-$&").toLowerCase(),"string"==typeof(n=e.getAttribute(i))){try{n="true"===(r=n)||"false"!==r&&("null"===r?null:r===+r+""?+r:ae.test(r)?JSON.parse(r):r)}catch(e){}oe.set(e,t,n)}else n=void 0;return n}T.extend({hasData:function(e){return oe.hasData(e)||re.hasData(e)},data:function(e,t,n){return oe.access(e,t,n)},removeData:function(e,t){oe.remove(e,t)},_data:function(e,t,n){return re.access(e,t,n)},_removeData:function(e,t){re.remove(e,t)}}),T.fn.extend({data:function(e,t){var n,i,r,o=this[0],a=o&&o.attributes;if(void 0===e){if(this.length&&(r=oe.get(o),1===o.nodeType&&!re.get(o,"hasDataAttrs"))){for(n=a.length;n--;)a[n]&&0===(i=a[n].name).indexOf("data-")&&(i=te(i.slice(5)),le(o,i,r[i]));re.set(o,"hasDataAttrs",!0)}return r}return"object"==typeof e?this.each(function(){oe.set(this,e)}):G(this,function(t){var n;if(o&&void 0===t)return void 0!==(n=oe.get(o,e))||void 0!==(n=le(o,e))?n:void 0;this.each(function(){oe.set(this,e,t)})},null,t,1<arguments.length,null,!0)},removeData:function(e){return this.each(function(){oe.remove(this,e)})}}),T.extend({queue:function(e,t,n){var i;if(e)return i=re.get(e,t=(t||"fx")+"queue"),n&&(!i||Array.isArray(n)?i=re.access(e,t,T.makeArray(n)):i.push(n)),i||[]},dequeue:function(e,t){var n=T.queue(e,t=t||"fx"),i=n.length,r=n.shift(),o=T._queueHooks(e,t);"inprogress"===r&&(r=n.shift(),i--),r&&("fx"===t&&n.unshift("inprogress"),delete o.stop,r.call(e,function(){T.dequeue(e,t)},o)),!i&&o&&o.empty.fire()},_queueHooks:function(e,t){var n=t+"queueHooks";return re.get(e,n)||re.access(e,n,{empty:T.Callbacks("once memory").add(function(){re.remove(e,[t+"queue",n])})})}}),T.fn.extend({queue:function(e,t){var n=2;return"string"!=typeof e&&(t=e,e="fx",n--),arguments.length<n?T.queue(this[0],e):void 0===t?this:this.each(function(){var n=T.queue(this,e,t);T._queueHooks(this,e),"fx"===e&&"inprogress"!==n[0]&&T.dequeue(this,e)})},dequeue:function(e){return this.each(function(){T.dequeue(this,e)})},clearQueue:function(e){return this.queue(e||"fx",[])},promise:function(e,t){var n,i=1,r=T.Deferred(),o=this,a=this.length,s=function(){--i||r.resolveWith(o,[o])};for("string"!=typeof e&&(t=e,e=void 0),e=e||"fx";a--;)(n=re.get(o[a],e+"queueHooks"))&&n.empty&&(i++,n.empty.add(s));return s(),r.promise(t)}});var ue=/[+-]?(?:\d*\.|)\d+(?:[eE][+-]?\d+|)/.source,ce=new RegExp("^(?:([+-])=|)("+ue+")([a-z%]*)$","i"),fe=["Top","Right","Bottom","Left"],de=m.documentElement,he=function(e){return T.contains(e.ownerDocument,e)},pe={composed:!0};de.getRootNode&&(he=function(e){return T.contains(e.ownerDocument,e)||e.getRootNode(pe)===e.ownerDocument});var ge=function(e,t){return"none"===(e=t||e).style.display||""===e.style.display&&he(e)&&"none"===T.css(e,"display")};function me(e,t,n,i){var r,o,a=20,s=i?function(){return i.cur()}:function(){return T.css(e,t,"")},l=s(),u=n&&n[3]||(T.cssNumber[t]?"":"px"),c=e.nodeType&&(T.cssNumber[t]||"px"!==u&&+l)&&ce.exec(T.css(e,t));if(c&&c[3]!==u){for(u=u||c[3],c=+(l/=2)||1;a--;)T.style(e,t,c+u),(1-o)*(1-(o=s()/l||.5))<=0&&(a=0),c/=o;T.style(e,t,(c*=2)+u),n=n||[]}return n&&(c=+c||+l||0,r=n[1]?c+(n[1]+1)*n[2]:+n[2],i&&(i.unit=u,i.start=c,i.end=r)),r}var ve={};function ye(e,t){for(var n,i,r,o,a,s,l,u=[],c=0,f=e.length;c<f;c++)(i=e[c]).style&&(n=i.style.display,t?("none"===n&&(u[c]=re.get(i,"display")||null,u[c]||(i.style.display="")),""===i.style.display&&ge(i)&&(u[c]=(l=a=o=void 0,a=(r=i).ownerDocument,(l=ve[s=r.nodeName])||(o=a.body.appendChild(a.createElement(s)),l=T.css(o,"display"),o.parentNode.removeChild(o),"none"===l&&(l="block"),ve[s]=l)))):"none"!==n&&(u[c]="none",re.set(i,"display",n)));for(c=0;c<f;c++)null!=u[c]&&(e[c].style.display=u[c]);return e}T.fn.extend({show:function(){return ye(this,!0)},hide:function(){return ye(this)},toggle:function(e){return"boolean"==typeof e?e?this.show():this.hide():this.each(function(){ge(this)?T(this).show():T(this).hide()})}});var _e,be,we=/^(?:checkbox|radio)$/i,Te=/<([a-z][^\/\0>\x20\t\r\n\f]*)/i,Ee=/^$|^module$|\/(?:java|ecma)script/i;_e=m.createDocumentFragment().appendChild(m.createElement("div")),(be=m.createElement("input")).setAttribute("type","radio"),be.setAttribute("checked","checked"),be.setAttribute("name","t"),_e.appendChild(be),h.checkClone=_e.cloneNode(!0).cloneNode(!0).lastChild.checked,_e.innerHTML="<textarea>x</textarea>",h.noCloneChecked=!!_e.cloneNode(!0).lastChild.defaultValue,_e.innerHTML="<option></option>",h.option=!!_e.lastChild;var xe={thead:[1,"<table>","</table>"],col:[2,"<table><colgroup>","</colgroup></table>"],tr:[2,"<table><tbody>","</tbody></table>"],td:[3,"<table><tbody><tr>","</tr></tbody></table>"],_default:[0,"",""]};function Ce(e,t){var n;return n=void 0!==e.getElementsByTagName?e.getElementsByTagName(t||"*"):void 0!==e.querySelectorAll?e.querySelectorAll(t||"*"):[],void 0===t||t&&x(e,t)?T.merge([e],n):n}function Se(e,t){for(var n=0,i=e.length;n<i;n++)re.set(e[n],"globalEval",!t||re.get(t[n],"globalEval"))}xe.tbody=xe.tfoot=xe.colgroup=xe.caption=xe.thead,xe.th=xe.td,h.option||(xe.optgroup=xe.option=[1,"<select multiple='multiple'>","</select>"]);var ke=/<|&#?\w+;/;function Ae(e,t,n,i,r){for(var o,a,s,l,u,c,f=t.createDocumentFragment(),d=[],h=0,p=e.length;h<p;h++)if((o=e[h])||0===o)if("object"===_(o))T.merge(d,o.nodeType?[o]:o);else if(ke.test(o)){for(a=a||f.appendChild(t.createElement("div")),s=(Te.exec(o)||["",""])[1].toLowerCase(),a.innerHTML=(l=xe[s]||xe._default)[1]+T.htmlPrefilter(o)+l[2],c=l[0];c--;)a=a.lastChild;T.merge(d,a.childNodes),(a=f.firstChild).textContent=""}else d.push(t.createTextNode(o));for(f.textContent="",h=0;o=d[h++];)if(i&&-1<T.inArray(o,i))r&&r.push(o);else if(u=he(o),a=Ce(f.appendChild(o),"script"),u&&Se(a),n)for(c=0;o=a[c++];)Ee.test(o.type||"")&&n.push(o);return f}var De=/^([^.]*)(?:\.(.+)|)/;function Ne(){return!0}function je(){return!1}function Ie(e,t,n,i,r,o){var a,s;if("object"==typeof t){for(s in"string"!=typeof n&&(i=i||n,n=void 0),t)Ie(e,s,n,i,t[s],o);return e}if(null==i&&null==r?(r=n,i=n=void 0):null==r&&("string"==typeof n?(r=i,i=void 0):(r=i,i=n,n=void 0)),!1===r)r=je;else if(!r)return e;return 1===o&&(a=r,(r=function(e){return T().off(e),a.apply(this,arguments)}).guid=a.guid||(a.guid=T.guid++)),e.each(function(){T.event.add(this,t,r,i,n)})}function Oe(e,t,n){n?(re.set(e,t,!1),T.event.add(e,t,{namespace:!1,handler:function(e){var n,i=re.get(this,t);if(1&e.isTrigger&&this[t]){if(i)(T.event.special[t]||{}).delegateType&&e.stopPropagation();else if(i=r.call(arguments),re.set(this,t,i),this[t](),n=re.get(this,t),re.set(this,t,!1),i!==n)return e.stopImmediatePropagation(),e.preventDefault(),n}else i&&(re.set(this,t,T.event.trigger(i[0],i.slice(1),this)),e.stopPropagation(),e.isImmediatePropagationStopped=Ne)}})):void 0===re.get(e,t)&&T.event.add(e,t,Ne)}T.event={global:{},add:function(e,t,n,i,r){var o,a,s,l,u,c,f,d,h,p,g,m=re.get(e);if(ne(e))for(n.handler&&(n=(o=n).handler,r=o.selector),r&&T.find.matchesSelector(de,r),n.guid||(n.guid=T.guid++),(l=m.events)||(l=m.events=Object.create(null)),(a=m.handle)||(a=m.handle=function(t){return void 0!==T&&T.event.triggered!==t.type?T.event.dispatch.apply(e,arguments):void 0}),u=(t=(t||"").match(Q)||[""]).length;u--;)h=g=(s=De.exec(t[u])||[])[1],p=(s[2]||"").split(".").sort(),h&&(f=T.event.special[h]||{},f=T.event.special[h=(r?f.delegateType:f.bindType)||h]||{},c=T.extend({type:h,origType:g,data:i,handler:n,guid:n.guid,selector:r,needsContext:r&&T.expr.match.needsContext.test(r),namespace:p.join(".")},o),(d=l[h])||((d=l[h]=[]).delegateCount=0,f.setup&&!1!==f.setup.call(e,i,p,a)||e.addEventListener&&e.addEventListener(h,a)),f.add&&(f.add.call(e,c),c.handler.guid||(c.handler.guid=n.guid)),r?d.splice(d.delegateCount++,0,c):d.push(c),T.event.global[h]=!0)},remove:function(e,t,n,i,r){var o,a,s,l,u,c,f,d,h,p,g,m=re.hasData(e)&&re.get(e);if(m&&(l=m.events)){for(u=(t=(t||"").match(Q)||[""]).length;u--;)if(h=g=(s=De.exec(t[u])||[])[1],p=(s[2]||"").split(".").sort(),h){for(f=T.event.special[h]||{},d=l[h=(i?f.delegateType:f.bindType)||h]||[],s=s[2]&&new RegExp("(^|\\.)"+p.join("\\.(?:.*\\.|)")+"(\\.|$)"),a=o=d.length;o--;)c=d[o],!r&&g!==c.origType||n&&n.guid!==c.guid||s&&!s.test(c.namespace)||i&&i!==c.selector&&("**"!==i||!c.selector)||(d.splice(o,1),c.selector&&d.delegateCount--,f.remove&&f.remove.call(e,c));a&&!d.length&&(f.teardown&&!1!==f.teardown.call(e,p,m.handle)||T.removeEvent(e,h,m.handle),delete l[h])}else for(h in l)T.event.remove(e,h+t[u],n,i,!0);T.isEmptyObject(l)&&re.remove(e,"handle events")}},dispatch:function(e){var t,n,i,r,o,a,s=new Array(arguments.length),l=T.event.fix(e),u=(re.get(this,"events")||Object.create(null))[l.type]||[],c=T.event.special[l.type]||{};for(s[0]=l,t=1;t<arguments.length;t++)s[t]=arguments[t];if(l.delegateTarget=this,!c.preDispatch||!1!==c.preDispatch.call(this,l)){for(a=T.event.handlers.call(this,l,u),t=0;(r=a[t++])&&!l.isPropagationStopped();)for(l.currentTarget=r.elem,n=0;(o=r.handlers[n++])&&!l.isImmediatePropagationStopped();)l.rnamespace&&!1!==o.namespace&&!l.rnamespace.test(o.namespace)||(l.handleObj=o,l.data=o.data,void 0!==(i=((T.event.special[o.origType]||{}).handle||o.handler).apply(r.elem,s))&&!1===(l.result=i)&&(l.preventDefault(),l.stopPropagation()));return c.postDispatch&&c.postDispatch.call(this,l),l.result}},handlers:function(e,t){var n,i,r,o,a,s=[],l=t.delegateCount,u=e.target;if(l&&u.nodeType&&!("click"===e.type&&1<=e.button))for(;u!==this;u=u.parentNode||this)if(1===u.nodeType&&("click"!==e.type||!0!==u.disabled)){for(o=[],a={},n=0;n<l;n++)void 0===a[r=(i=t[n]).selector+" "]&&(a[r]=i.needsContext?-1<T(r,this).index(u):T.find(r,this,null,[u]).length),a[r]&&o.push(i);o.length&&s.push({elem:u,handlers:o})}return u=this,l<t.length&&s.push({elem:u,handlers:t.slice(l)}),s},addProp:function(e,t){Object.defineProperty(T.Event.prototype,e,{enumerable:!0,configurable:!0,get:p(t)?function(){if(this.originalEvent)return t(this.originalEvent)}:function(){if(this.originalEvent)return this.originalEvent[e]},set:function(t){Object.defineProperty(this,e,{enumerable:!0,configurable:!0,writable:!0,value:t})}})},fix:function(e){return e[T.expando]?e:new T.Event(e)},special:{load:{noBubble:!0},click:{setup:function(e){var t=this||e;return we.test(t.type)&&t.click&&x(t,"input")&&Oe(t,"click",!0),!1},trigger:function(e){var t=this||e;return we.test(t.type)&&t.click&&x(t,"input")&&Oe(t,"click"),!0},_default:function(e){var t=e.target;return we.test(t.type)&&t.click&&x(t,"input")&&re.get(t,"click")||x(t,"a")}},beforeunload:{postDispatch:function(e){void 0!==e.result&&e.originalEvent&&(e.originalEvent.returnValue=e.result)}}}},T.removeEvent=function(e,t,n){e.removeEventListener&&e.removeEventListener(t,n)},T.Event=function(e,t){if(!(this instanceof T.Event))return new T.Event(e,t);e&&e.type?(this.originalEvent=e,this.type=e.type,this.isDefaultPrevented=e.defaultPrevented||void 0===e.defaultPrevented&&!1===e.returnValue?Ne:je,this.target=e.target&&3===e.target.nodeType?e.target.parentNode:e.target,this.currentTarget=e.currentTarget,this.relatedTarget=e.relatedTarget):this.type=e,t&&T.extend(this,t),this.timeStamp=e&&e.timeStamp||Date.now(),this[T.expando]=!0},T.Event.prototype={constructor:T.Event,isDefaultPrevented:je,isPropagationStopped:je,isImmediatePropagationStopped:je,isSimulated:!1,preventDefault:function(){var e=this.originalEvent;this.isDefaultPrevented=Ne,e&&!this.isSimulated&&e.preventDefault()},stopPropagation:function(){var e=this.originalEvent;this.isPropagationStopped=Ne,e&&!this.isSimulated&&e.stopPropagation()},stopImmediatePropagation:function(){var e=this.originalEvent;this.isImmediatePropagationStopped=Ne,e&&!this.isSimulated&&e.stopImmediatePropagation(),this.stopPropagation()}},T.each({altKey:!0,bubbles:!0,cancelable:!0,changedTouches:!0,ctrlKey:!0,detail:!0,eventPhase:!0,metaKey:!0,pageX:!0,pageY:!0,shiftKey:!0,view:!0,char:!0,code:!0,charCode:!0,key:!0,keyCode:!0,button:!0,buttons:!0,clientX:!0,clientY:!0,offsetX:!0,offsetY:!0,pointerId:!0,pointerType:!0,screenX:!0,screenY:!0,targetTouches:!0,toElement:!0,touches:!0,which:!0},T.event.addProp),T.each({focus:"focusin",blur:"focusout"},function(e,t){function n(e){if(m.documentMode){var n=re.get(this,"handle"),i=T.event.fix(e);i.type="focusin"===e.type?"focus":"blur",i.isSimulated=!0,n(e),i.target===i.currentTarget&&n(i)}else T.event.simulate(t,e.target,T.event.fix(e))}T.event.special[e]={setup:function(){var i;if(Oe(this,e,!0),!m.documentMode)return!1;(i=re.get(this,t))||this.addEventListener(t,n),re.set(this,t,(i||0)+1)},trigger:function(){return Oe(this,e),!0},teardown:function(){var e;if(!m.documentMode)return!1;(e=re.get(this,t)-1)?re.set(this,t,e):(this.removeEventListener(t,n),re.remove(this,t))},_default:function(t){return re.get(t.target,e)},delegateType:t},T.event.special[t]={setup:function(){var i=this.ownerDocument||this.document||this,r=m.documentMode?this:i,o=re.get(r,t);o||(m.documentMode?this.addEventListener(t,n):i.addEventListener(e,n,!0)),re.set(r,t,(o||0)+1)},teardown:function(){var i=this.ownerDocument||this.document||this,r=m.documentMode?this:i,o=re.get(r,t)-1;o?re.set(r,t,o):(m.documentMode?this.removeEventListener(t,n):i.removeEventListener(e,n,!0),re.remove(r,t))}}}),T.each({mouseenter:"mouseover",mouseleave:"mouseout",pointerenter:"pointerover",pointerleave:"pointerout"},function(e,t){T.event.special[e]={delegateType:t,bindType:t,handle:function(e){var n,i=e.relatedTarget,r=e.handleObj;return i&&(i===this||T.contains(this,i))||(e.type=r.origType,n=r.handler.apply(this,arguments),e.type=t),n}}}),T.fn.extend({on:function(e,t,n,i){return Ie(this,e,t,n,i)},one:function(e,t,n,i){return Ie(this,e,t,n,i,1)},off:function(e,t,n){var i,r;if(e&&e.preventDefault&&e.handleObj)return i=e.handleObj,T(e.delegateTarget).off(i.namespace?i.origType+"."+i.namespace:i.origType,i.selector,i.handler),this;if("object"==typeof e){for(r in e)this.off(r,t,e[r]);return this}return!1!==t&&"function"!=typeof t||(n=t,t=void 0),!1===n&&(n=je),this.each(function(){T.event.remove(this,e,n,t)})}});var Le=/<script|<style|<link/i,qe=/checked\s*(?:[^=]|=\s*.checked.)/i,Pe=/^\s*<!\[CDATA\[|\]\]>\s*$/g;function Re(e,t){return x(e,"table")&&x(11!==t.nodeType?t:t.firstChild,"tr")&&T(e).children("tbody")[0]||e}function He(e){return e.type=(null!==e.getAttribute("type"))+"/"+e.type,e}function Fe(e){return"true/"===(e.type||"").slice(0,5)?e.type=e.type.slice(5):e.removeAttribute("type"),e}function Me(e,t){var n,i,r,o,a,s;if(1===t.nodeType){if(re.hasData(e)&&(s=re.get(e).events))for(r in re.remove(t,"handle events"),s)for(n=0,i=s[r].length;n<i;n++)T.event.add(t,r,s[r][n]);oe.hasData(e)&&(o=oe.access(e),a=T.extend({},o),oe.set(t,a))}}function Be(e,t,n,i){t=o(t);var r,a,s,l,u,c,f=0,d=e.length,g=d-1,m=t[0],v=p(m);if(v||1<d&&"string"==typeof m&&!h.checkClone&&qe.test(m))return e.each(function(r){var o=e.eq(r);v&&(t[0]=m.call(this,r,o.html())),Be(o,t,n,i)});if(d&&(a=(r=Ae(t,e[0].ownerDocument,!1,e,i)).firstChild,1===r.childNodes.length&&(r=a),a||i)){for(l=(s=T.map(Ce(r,"script"),He)).length;f<d;f++)u=r,f!==g&&(u=T.clone(u,!0,!0),l&&T.merge(s,Ce(u,"script"))),n.call(e[f],u,f);if(l)for(c=s[s.length-1].ownerDocument,T.map(s,Fe),f=0;f<l;f++)Ee.test((u=s[f]).type||"")&&!re.access(u,"globalEval")&&T.contains(c,u)&&(u.src&&"module"!==(u.type||"").toLowerCase()?T._evalUrl&&!u.noModule&&T._evalUrl(u.src,{nonce:u.nonce||u.getAttribute("nonce")},c):y(u.textContent.replace(Pe,""),u,c))}return e}function We(e,t,n){for(var i,r=t?T.filter(t,e):e,o=0;null!=(i=r[o]);o++)n||1!==i.nodeType||T.cleanData(Ce(i)),i.parentNode&&(n&&he(i)&&Se(Ce(i,"script")),i.parentNode.removeChild(i));return e}T.extend({htmlPrefilter:function(e){return e},clone:function(e,t,n){var i,r,o,a,s,l,u,c=e.cloneNode(!0),f=he(e);if(!(h.noCloneChecked||1!==e.nodeType&&11!==e.nodeType||T.isXMLDoc(e)))for(a=Ce(c),i=0,r=(o=Ce(e)).length;i<r;i++)s=o[i],"input"===(u=(l=a[i]).nodeName.toLowerCase())&&we.test(s.type)?l.checked=s.checked:"input"!==u&&"textarea"!==u||(l.defaultValue=s.defaultValue);if(t)if(n)for(o=o||Ce(e),a=a||Ce(c),i=0,r=o.length;i<r;i++)Me(o[i],a[i]);else Me(e,c);return 0<(a=Ce(c,"script")).length&&Se(a,!f&&Ce(e,"script")),c},cleanData:function(e){for(var t,n,i,r=T.event.special,o=0;void 0!==(n=e[o]);o++)if(ne(n)){if(t=n[re.expando]){if(t.events)for(i in t.events)r[i]?T.event.remove(n,i):T.removeEvent(n,i,t.handle);n[re.expando]=void 0}n[oe.expando]&&(n[oe.expando]=void 0)}}}),T.fn.extend({detach:function(e){return We(this,e,!0)},remove:function(e){return We(this,e)},text:function(e){return G(this,function(e){return void 0===e?T.text(this):this.empty().each(function(){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||(this.textContent=e)})},null,e,arguments.length)},append:function(){return Be(this,arguments,function(e){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||Re(this,e).appendChild(e)})},prepend:function(){return Be(this,arguments,function(e){if(1===this.nodeType||11===this.nodeType||9===this.nodeType){var t=Re(this,e);t.insertBefore(e,t.firstChild)}})},before:function(){return Be(this,arguments,function(e){this.parentNode&&this.parentNode.insertBefore(e,this)})},after:function(){return Be(this,arguments,function(e){this.parentNode&&this.parentNode.insertBefore(e,this.nextSibling)})},empty:function(){for(var e,t=0;null!=(e=this[t]);t++)1===e.nodeType&&(T.cleanData(Ce(e,!1)),e.textContent="");return this},clone:function(e,t){return e=null!=e&&e,t=null==t?e:t,this.map(function(){return T.clone(this,e,t)})},html:function(e){return G(this,function(e){var t=this[0]||{},n=0,i=this.length;if(void 0===e&&1===t.nodeType)return t.innerHTML;if("string"==typeof e&&!Le.test(e)&&!xe[(Te.exec(e)||["",""])[1].toLowerCase()]){e=T.htmlPrefilter(e);try{for(;n<i;n++)1===(t=this[n]||{}).nodeType&&(T.cleanData(Ce(t,!1)),t.innerHTML=e);t=0}catch(e){}}t&&this.empty().append(e)},null,e,arguments.length)},replaceWith:function(){var e=[];return Be(this,arguments,function(t){var n=this.parentNode;T.inArray(this,e)<0&&(T.cleanData(Ce(this)),n&&n.replaceChild(t,this))},e)}}),T.each({appendTo:"append",prependTo:"prepend",insertBefore:"before",insertAfter:"after",replaceAll:"replaceWith"},function(e,t){T.fn[e]=function(e){for(var n,i=[],r=T(e),o=r.length-1,s=0;s<=o;s++)n=s===o?this:this.clone(!0),T(r[s])[t](n),a.apply(i,n.get());return this.pushStack(i)}});var Ue=new RegExp("^("+ue+")(?!px)[a-z%]+$","i"),Qe=/^--/,$e=function(t){var n=t.ownerDocument.defaultView;return n&&n.opener||(n=e),n.getComputedStyle(t)},ze=function(e,t,n){var i,r,o={};for(r in t)o[r]=e.style[r],e.style[r]=t[r];for(r in i=n.call(e),t)e.style[r]=o[r];return i},Xe=new RegExp(fe.join("|"),"i");function Ve(e,t,n){var i,r,o,a,s=Qe.test(t),l=e.style;return(n=n||$e(e))&&(a=n.getPropertyValue(t)||n[t],s&&a&&(a=a.replace(D,"$1")||void 0),""!==a||he(e)||(a=T.style(e,t)),!h.pixelBoxStyles()&&Ue.test(a)&&Xe.test(t)&&(i=l.width,r=l.minWidth,o=l.maxWidth,l.minWidth=l.maxWidth=l.width=a,a=n.width,l.width=i,l.minWidth=r,l.maxWidth=o)),void 0!==a?a+"":a}function Ye(e,t){return{get:function(){if(!e())return(this.get=t).apply(this,arguments);delete this.get}}}!function(){function t(){if(c){u.style.cssText="position:absolute;left:-11111px;width:60px;margin-top:1px;padding:0;border:0",c.style.cssText="position:relative;display:block;box-sizing:border-box;overflow:scroll;margin:auto;border:1px;padding:1px;width:60%;top:1%",de.appendChild(u).appendChild(c);var t=e.getComputedStyle(c);i="1%"!==t.top,l=12===n(t.marginLeft),c.style.right="60%",a=36===n(t.right),r=36===n(t.width),c.style.position="absolute",o=12===n(c.offsetWidth/3),de.removeChild(u),c=null}}function n(e){return Math.round(parseFloat(e))}var i,r,o,a,s,l,u=m.createElement("div"),c=m.createElement("div");c.style&&(c.style.backgroundClip="content-box",c.cloneNode(!0).style.backgroundClip="",h.clearCloneStyle="content-box"===c.style.backgroundClip,T.extend(h,{boxSizingReliable:function(){return t(),r},pixelBoxStyles:function(){return t(),a},pixelPosition:function(){return t(),i},reliableMarginLeft:function(){return t(),l},scrollboxSize:function(){return t(),o},reliableTrDimensions:function(){var t,n,i,r;return null==s&&(t=m.createElement("table"),n=m.createElement("tr"),i=m.createElement("div"),t.style.cssText="position:absolute;left:-11111px;border-collapse:separate",n.style.cssText="box-sizing:content-box;border:1px solid",n.style.height="1px",i.style.height="9px",i.style.display="block",de.appendChild(t).appendChild(n).appendChild(i),r=e.getComputedStyle(n),s=parseInt(r.height,10)+parseInt(r.borderTopWidth,10)+parseInt(r.borderBottomWidth,10)===n.offsetHeight,de.removeChild(t)),s}}))}();var Ke=["Webkit","Moz","ms"],Ge=m.createElement("div").style,Je={};function Ze(e){return T.cssProps[e]||Je[e]||(e in Ge?e:Je[e]=function(e){for(var t=e[0].toUpperCase()+e.slice(1),n=Ke.length;n--;)if((e=Ke[n]+t)in Ge)return e}(e)||e)}var et=/^(none|table(?!-c[ea]).+)/,tt={position:"absolute",visibility:"hidden",display:"block"},nt={letterSpacing:"0",fontWeight:"400"};function it(e,t,n){var i=ce.exec(t);return i?Math.max(0,i[2]-(n||0))+(i[3]||"px"):t}function rt(e,t,n,i,r,o){var a="width"===t?1:0,s=0,l=0,u=0;if(n===(i?"border":"content"))return 0;for(;a<4;a+=2)"margin"===n&&(u+=T.css(e,n+fe[a],!0,r)),i?("content"===n&&(l-=T.css(e,"padding"+fe[a],!0,r)),"margin"!==n&&(l-=T.css(e,"border"+fe[a]+"Width",!0,r))):(l+=T.css(e,"padding"+fe[a],!0,r),"padding"!==n?l+=T.css(e,"border"+fe[a]+"Width",!0,r):s+=T.css(e,"border"+fe[a]+"Width",!0,r));return!i&&0<=o&&(l+=Math.max(0,Math.ceil(e["offset"+t[0].toUpperCase()+t.slice(1)]-o-l-s-.5))||0),l+u}function ot(e,t,n){var i=$e(e),r=(!h.boxSizingReliable()||n)&&"border-box"===T.css(e,"boxSizing",!1,i),o=r,a=Ve(e,t,i),s="offset"+t[0].toUpperCase()+t.slice(1);if(Ue.test(a)){if(!n)return a;a="auto"}return(!h.boxSizingReliable()&&r||!h.reliableTrDimensions()&&x(e,"tr")||"auto"===a||!parseFloat(a)&&"inline"===T.css(e,"display",!1,i))&&e.getClientRects().length&&(r="border-box"===T.css(e,"boxSizing",!1,i),(o=s in e)&&(a=e[s])),(a=parseFloat(a)||0)+rt(e,t,n||(r?"border":"content"),o,i,a)+"px"}function at(e,t,n,i,r){return new at.prototype.init(e,t,n,i,r)}T.extend({cssHooks:{opacity:{get:function(e,t){if(t){var n=Ve(e,"opacity");return""===n?"1":n}}}},cssNumber:{animationIterationCount:!0,aspectRatio:!0,borderImageSlice:!0,columnCount:!0,flexGrow:!0,flexShrink:!0,fontWeight:!0,gridArea:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnStart:!0,gridRow:!0,gridRowEnd:!0,gridRowStart:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,scale:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeMiterlimit:!0,strokeOpacity:!0},cssProps:{},style:function(e,t,n,i){if(e&&3!==e.nodeType&&8!==e.nodeType&&e.style){var r,o,a,s=te(t),l=Qe.test(t),u=e.style;if(l||(t=Ze(s)),a=T.cssHooks[t]||T.cssHooks[s],void 0===n)return a&&"get"in a&&void 0!==(r=a.get(e,!1,i))?r:u[t];"string"==(o=typeof n)&&(r=ce.exec(n))&&r[1]&&(n=me(e,t,r),o="number"),null!=n&&n==n&&("number"!==o||l||(n+=r&&r[3]||(T.cssNumber[s]?"":"px")),h.clearCloneStyle||""!==n||0!==t.indexOf("background")||(u[t]="inherit"),a&&"set"in a&&void 0===(n=a.set(e,n,i))||(l?u.setProperty(t,n):u[t]=n))}},css:function(e,t,n,i){var r,o,a,s=te(t);return Qe.test(t)||(t=Ze(s)),(a=T.cssHooks[t]||T.cssHooks[s])&&"get"in a&&(r=a.get(e,!0,n)),void 0===r&&(r=Ve(e,t,i)),"normal"===r&&t in nt&&(r=nt[t]),""===n||n?(o=parseFloat(r),!0===n||isFinite(o)?o||0:r):r}}),T.each(["height","width"],function(e,t){T.cssHooks[t]={get:function(e,n,i){if(n)return!et.test(T.css(e,"display"))||e.getClientRects().length&&e.getBoundingClientRect().width?ot(e,t,i):ze(e,tt,function(){return ot(e,t,i)})},set:function(e,n,i){var r,o=$e(e),a=!h.scrollboxSize()&&"absolute"===o.position,s=(a||i)&&"border-box"===T.css(e,"boxSizing",!1,o),l=i?rt(e,t,i,s,o):0;return s&&a&&(l-=Math.ceil(e["offset"+t[0].toUpperCase()+t.slice(1)]-parseFloat(o[t])-rt(e,t,"border",!1,o)-.5)),l&&(r=ce.exec(n))&&"px"!==(r[3]||"px")&&(e.style[t]=n,n=T.css(e,t)),it(0,n,l)}}}),T.cssHooks.marginLeft=Ye(h.reliableMarginLeft,function(e,t){if(t)return(parseFloat(Ve(e,"marginLeft"))||e.getBoundingClientRect().left-ze(e,{marginLeft:0},function(){return e.getBoundingClientRect().left}))+"px"}),T.each({margin:"",padding:"",border:"Width"},function(e,t){T.cssHooks[e+t]={expand:function(n){for(var i=0,r={},o="string"==typeof n?n.split(" "):[n];i<4;i++)r[e+fe[i]+t]=o[i]||o[i-2]||o[0];return r}},"margin"!==e&&(T.cssHooks[e+t].set=it)}),T.fn.extend({css:function(e,t){return G(this,function(e,t,n){var i,r,o={},a=0;if(Array.isArray(t)){for(i=$e(e),r=t.length;a<r;a++)o[t[a]]=T.css(e,t[a],!1,i);return o}return void 0!==n?T.style(e,t,n):T.css(e,t)},e,t,1<arguments.length)}}),((T.Tween=at).prototype={constructor:at,init:function(e,t,n,i,r,o){this.elem=e,this.prop=n,this.easing=r||T.easing._default,this.options=t,this.start=this.now=this.cur(),this.end=i,this.unit=o||(T.cssNumber[n]?"":"px")},cur:function(){var e=at.propHooks[this.prop];return e&&e.get?e.get(this):at.propHooks._default.get(this)},run:function(e){var t,n=at.propHooks[this.prop];return this.pos=t=this.options.duration?T.easing[this.easing](e,this.options.duration*e,0,1,this.options.duration):e,this.now=(this.end-this.start)*t+this.start,this.options.step&&this.options.step.call(this.elem,this.now,this),n&&n.set?n.set(this):at.propHooks._default.set(this),this}}).init.prototype=at.prototype,(at.propHooks={_default:{get:function(e){var t;return 1!==e.elem.nodeType||null!=e.elem[e.prop]&&null==e.elem.style[e.prop]?e.elem[e.prop]:(t=T.css(e.elem,e.prop,""))&&"auto"!==t?t:0},set:function(e){T.fx.step[e.prop]?T.fx.step[e.prop](e):1!==e.elem.nodeType||!T.cssHooks[e.prop]&&null==e.elem.style[Ze(e.prop)]?e.elem[e.prop]=e.now:T.style(e.elem,e.prop,e.now+e.unit)}}}).scrollTop=at.propHooks.scrollLeft={set:function(e){e.elem.nodeType&&e.elem.parentNode&&(e.elem[e.prop]=e.now)}},T.easing={linear:function(e){return e},swing:function(e){return.5-Math.cos(e*Math.PI)/2},_default:"swing"},T.fx=at.prototype.init,T.fx.step={};var st,lt,ut,ct,ft=/^(?:toggle|show|hide)$/,dt=/queueHooks$/;function ht(){lt&&(!1===m.hidden&&e.requestAnimationFrame?e.requestAnimationFrame(ht):e.setTimeout(ht,T.fx.interval),T.fx.tick())}function pt(){return e.setTimeout(function(){st=void 0}),st=Date.now()}function gt(e,t){var n,i=0,r={height:e};for(t=t?1:0;i<4;i+=2-t)r["margin"+(n=fe[i])]=r["padding"+n]=e;return t&&(r.opacity=r.width=e),r}function mt(e,t,n){for(var i,r=(vt.tweeners[t]||[]).concat(vt.tweeners["*"]),o=0,a=r.length;o<a;o++)if(i=r[o].call(n,t,e))return i}function vt(e,t,n){var i,r,o=0,a=vt.prefilters.length,s=T.Deferred().always(function(){delete l.elem}),l=function(){if(r)return!1;for(var t=st||pt(),n=Math.max(0,u.startTime+u.duration-t),i=1-(n/u.duration||0),o=0,a=u.tweens.length;o<a;o++)u.tweens[o].run(i);return s.notifyWith(e,[u,i,n]),i<1&&a?n:(a||s.notifyWith(e,[u,1,0]),s.resolveWith(e,[u]),!1)},u=s.promise({elem:e,props:T.extend({},t),opts:T.extend(!0,{specialEasing:{},easing:T.easing._default},n),originalProperties:t,originalOptions:n,startTime:st||pt(),duration:n.duration,tweens:[],createTween:function(t,n){var i=T.Tween(e,u.opts,t,n,u.opts.specialEasing[t]||u.opts.easing);return u.tweens.push(i),i},stop:function(t){var n=0,i=t?u.tweens.length:0;if(r)return this;for(r=!0;n<i;n++)u.tweens[n].run(1);return t?(s.notifyWith(e,[u,1,0]),s.resolveWith(e,[u,t])):s.rejectWith(e,[u,t]),this}}),c=u.props;for(function(e,t){var n,i,r,o,a;for(n in e)if(r=t[i=te(n)],o=e[n],Array.isArray(o)&&(r=o[1],o=e[n]=o[0]),n!==i&&(e[i]=o,delete e[n]),(a=T.cssHooks[i])&&"expand"in a)for(n in o=a.expand(o),delete e[i],o)n in e||(e[n]=o[n],t[n]=r);else t[i]=r}(c,u.opts.specialEasing);o<a;o++)if(i=vt.prefilters[o].call(u,e,c,u.opts))return p(i.stop)&&(T._queueHooks(u.elem,u.opts.queue).stop=i.stop.bind(i)),i;return T.map(c,mt,u),p(u.opts.start)&&u.opts.start.call(e,u),u.progress(u.opts.progress).done(u.opts.done,u.opts.complete).fail(u.opts.fail).always(u.opts.always),T.fx.timer(T.extend(l,{elem:e,anim:u,queue:u.opts.queue})),u}T.Animation=T.extend(vt,{tweeners:{"*":[function(e,t){var n=this.createTween(e,t);return me(n.elem,e,ce.exec(t),n),n}]},tweener:function(e,t){p(e)?(t=e,e=["*"]):e=e.match(Q);for(var n,i=0,r=e.length;i<r;i++)(vt.tweeners[n=e[i]]=vt.tweeners[n]||[]).unshift(t)},prefilters:[function(e,t,n){var i,r,o,a,s,l,u,c,f="width"in t||"height"in t,d=this,h={},p=e.style,g=e.nodeType&&ge(e),m=re.get(e,"fxshow");for(i in n.queue||(null==(a=T._queueHooks(e,"fx")).unqueued&&(a.unqueued=0,s=a.empty.fire,a.empty.fire=function(){a.unqueued||s()}),a.unqueued++,d.always(function(){d.always(function(){a.unqueued--,T.queue(e,"fx").length||a.empty.fire()})})),t)if(ft.test(r=t[i])){if(delete t[i],o=o||"toggle"===r,r===(g?"hide":"show")){if("show"!==r||!m||void 0===m[i])continue;g=!0}h[i]=m&&m[i]||T.style(e,i)}if((l=!T.isEmptyObject(t))||!T.isEmptyObject(h))for(i in f&&1===e.nodeType&&(n.overflow=[p.overflow,p.overflowX,p.overflowY],null==(u=m&&m.display)&&(u=re.get(e,"display")),"none"===(c=T.css(e,"display"))&&(u?c=u:(ye([e],!0),u=e.style.display||u,c=T.css(e,"display"),ye([e]))),("inline"===c||"inline-block"===c&&null!=u)&&"none"===T.css(e,"float")&&(l||(d.done(function(){p.display=u}),null==u&&(u="none"===(c=p.display)?"":c)),p.display="inline-block")),n.overflow&&(p.overflow="hidden",d.always(function(){p.overflow=n.overflow[0],p.overflowX=n.overflow[1],p.overflowY=n.overflow[2]})),l=!1,h)l||(m?"hidden"in m&&(g=m.hidden):m=re.access(e,"fxshow",{display:u}),o&&(m.hidden=!g),g&&ye([e],!0),d.done(function(){for(i in g||ye([e]),re.remove(e,"fxshow"),h)T.style(e,i,h[i])})),l=mt(g?m[i]:0,i,d),i in m||(m[i]=l.start,g&&(l.end=l.start,l.start=0))}],prefilter:function(e,t){t?vt.prefilters.unshift(e):vt.prefilters.push(e)}}),T.speed=function(e,t,n){var i=e&&"object"==typeof e?T.extend({},e):{complete:n||!n&&t||p(e)&&e,duration:e,easing:n&&t||t&&!p(t)&&t};return T.fx.off?i.duration=0:"number"!=typeof i.duration&&(i.duration=i.duration in T.fx.speeds?T.fx.speeds[i.duration]:T.fx.speeds._default),null!=i.queue&&!0!==i.queue||(i.queue="fx"),i.old=i.complete,i.complete=function(){p(i.old)&&i.old.call(this),i.queue&&T.dequeue(this,i.queue)},i},T.fn.extend({fadeTo:function(e,t,n,i){return this.filter(ge).css("opacity",0).show().end().animate({opacity:t},e,n,i)},animate:function(e,t,n,i){var r=T.isEmptyObject(e),o=T.speed(t,n,i),a=function(){var t=vt(this,T.extend({},e),o);(r||re.get(this,"finish"))&&t.stop(!0)};return a.finish=a,r||!1===o.queue?this.each(a):this.queue(o.queue,a)},stop:function(e,t,n){var i=function(e){var t=e.stop;delete e.stop,t(n)};return"string"!=typeof e&&(n=t,t=e,e=void 0),t&&this.queue(e||"fx",[]),this.each(function(){var t=!0,r=null!=e&&e+"queueHooks",o=T.timers,a=re.get(this);if(r)a[r]&&a[r].stop&&i(a[r]);else for(r in a)a[r]&&a[r].stop&&dt.test(r)&&i(a[r]);for(r=o.length;r--;)o[r].elem!==this||null!=e&&o[r].queue!==e||(o[r].anim.stop(n),t=!1,o.splice(r,1));!t&&n||T.dequeue(this,e)})},finish:function(e){return!1!==e&&(e=e||"fx"),this.each(function(){var t,n=re.get(this),i=n[e+"queue"],r=n[e+"queueHooks"],o=T.timers,a=i?i.length:0;for(n.finish=!0,T.queue(this,e,[]),r&&r.stop&&r.stop.call(this,!0),t=o.length;t--;)o[t].elem===this&&o[t].queue===e&&(o[t].anim.stop(!0),o.splice(t,1));for(t=0;t<a;t++)i[t]&&i[t].finish&&i[t].finish.call(this);delete n.finish})}}),T.each(["toggle","show","hide"],function(e,t){var n=T.fn[t];T.fn[t]=function(e,i,r){return null==e||"boolean"==typeof e?n.apply(this,arguments):this.animate(gt(t,!0),e,i,r)}}),T.each({slideDown:gt("show"),slideUp:gt("hide"),slideToggle:gt("toggle"),fadeIn:{opacity:"show"},fadeOut:{opacity:"hide"},fadeToggle:{opacity:"toggle"}},function(e,t){T.fn[e]=function(e,n,i){return this.animate(t,e,n,i)}}),T.timers=[],T.fx.tick=function(){var e,t=0,n=T.timers;for(st=Date.now();t<n.length;t++)(e=n[t])()||n[t]!==e||n.splice(t--,1);n.length||T.fx.stop(),st=void 0},T.fx.timer=function(e){T.timers.push(e),T.fx.start()},T.fx.interval=13,T.fx.start=function(){lt||(lt=!0,ht())},T.fx.stop=function(){lt=null},T.fx.speeds={slow:600,fast:200,_default:400},T.fn.delay=function(t,n){return t=T.fx&&T.fx.speeds[t]||t,this.queue(n=n||"fx",function(n,i){var r=e.setTimeout(n,t);i.stop=function(){e.clearTimeout(r)}})},ut=m.createElement("input"),ct=m.createElement("select").appendChild(m.createElement("option")),ut.type="checkbox",h.checkOn=""!==ut.value,h.optSelected=ct.selected,(ut=m.createElement("input")).value="t",ut.type="radio",h.radioValue="t"===ut.value;var yt,_t=T.expr.attrHandle;T.fn.extend({attr:function(e,t){return G(this,T.attr,e,t,1<arguments.length)},removeAttr:function(e){return this.each(function(){T.removeAttr(this,e)})}}),T.extend({attr:function(e,t,n){var i,r,o=e.nodeType;if(3!==o&&8!==o&&2!==o)return void 0===e.getAttribute?T.prop(e,t,n):(1===o&&T.isXMLDoc(e)||(r=T.attrHooks[t.toLowerCase()]||(T.expr.match.bool.test(t)?yt:void 0)),void 0!==n?null===n?void T.removeAttr(e,t):r&&"set"in r&&void 0!==(i=r.set(e,n,t))?i:(e.setAttribute(t,n+""),n):r&&"get"in r&&null!==(i=r.get(e,t))?i:null==(i=T.find.attr(e,t))?void 0:i)},attrHooks:{type:{set:function(e,t){if(!h.radioValue&&"radio"===t&&x(e,"input")){var n=e.value;return e.setAttribute("type",t),n&&(e.value=n),t}}}},removeAttr:function(e,t){var n,i=0,r=t&&t.match(Q);if(r&&1===e.nodeType)for(;n=r[i++];)e.removeAttribute(n)}}),yt={set:function(e,t,n){return!1===t?T.removeAttr(e,n):e.setAttribute(n,n),n}},T.each(T.expr.match.bool.source.match(/\w+/g),function(e,t){var n=_t[t]||T.find.attr;_t[t]=function(e,t,i){var r,o,a=t.toLowerCase();return i||(o=_t[a],_t[a]=r,r=null!=n(e,t,i)?a:null,_t[a]=o),r}});var bt=/^(?:input|select|textarea|button)$/i,wt=/^(?:a|area)$/i;function Tt(e){return(e.match(Q)||[]).join(" ")}function Et(e){return e.getAttribute&&e.getAttribute("class")||""}function xt(e){return Array.isArray(e)?e:"string"==typeof e&&e.match(Q)||[]}T.fn.extend({prop:function(e,t){return G(this,T.prop,e,t,1<arguments.length)},removeProp:function(e){return this.each(function(){delete this[T.propFix[e]||e]})}}),T.extend({prop:function(e,t,n){var i,r,o=e.nodeType;if(3!==o&&8!==o&&2!==o)return 1===o&&T.isXMLDoc(e)||(r=T.propHooks[t=T.propFix[t]||t]),void 0!==n?r&&"set"in r&&void 0!==(i=r.set(e,n,t))?i:e[t]=n:r&&"get"in r&&null!==(i=r.get(e,t))?i:e[t]},propHooks:{tabIndex:{get:function(e){var t=T.find.attr(e,"tabindex");return t?parseInt(t,10):bt.test(e.nodeName)||wt.test(e.nodeName)&&e.href?0:-1}}},propFix:{for:"htmlFor",class:"className"}}),h.optSelected||(T.propHooks.selected={get:function(e){return null},set:function(e){}}),T.each(["tabIndex","readOnly","maxLength","cellSpacing","cellPadding","rowSpan","colSpan","useMap","frameBorder","contentEditable"],function(){T.propFix[this.toLowerCase()]=this}),T.fn.extend({addClass:function(e){var t,n,i,r,o,a;return p(e)?this.each(function(t){T(this).addClass(e.call(this,t,Et(this)))}):(t=xt(e)).length?this.each(function(){if(i=Et(this),n=1===this.nodeType&&" "+Tt(i)+" "){for(o=0;o<t.length;o++)n.indexOf(" "+(r=t[o])+" ")<0&&(n+=r+" ");a=Tt(n),i!==a&&this.setAttribute("class",a)}}):this},removeClass:function(e){var t,n,i,r,o,a;return p(e)?this.each(function(t){T(this).removeClass(e.call(this,t,Et(this)))}):arguments.length?(t=xt(e)).length?this.each(function(){if(i=Et(this),n=1===this.nodeType&&" "+Tt(i)+" "){for(o=0;o<t.length;o++)for(r=t[o];-1<n.indexOf(" "+r+" ");)n=n.replace(" "+r+" "," ");a=Tt(n),i!==a&&this.setAttribute("class",a)}}):this:this.attr("class","")},toggleClass:function(e,t){var n,i,r,o,a=typeof e,s="string"===a||Array.isArray(e);return p(e)?this.each(function(n){T(this).toggleClass(e.call(this,n,Et(this),t),t)}):"boolean"==typeof t&&s?t?this.addClass(e):this.removeClass(e):(n=xt(e),this.each(function(){if(s)for(o=T(this),r=0;r<n.length;r++)o.hasClass(i=n[r])?o.removeClass(i):o.addClass(i);else void 0!==e&&"boolean"!==a||((i=Et(this))&&re.set(this,"__className__",i),this.setAttribute&&this.setAttribute("class",i||!1===e?"":re.get(this,"__className__")||""))}))},hasClass:function(e){var t,n,i=0;for(t=" "+e+" ";n=this[i++];)if(1===n.nodeType&&-1<(" "+Tt(Et(n))+" ").indexOf(t))return!0;return!1}});var Ct=/\r/g;T.fn.extend({val:function(e){var t,n,i,r=this[0];return arguments.length?(i=p(e),this.each(function(n){var r;1===this.nodeType&&(null==(r=i?e.call(this,n,T(this).val()):e)?r="":"number"==typeof r?r+="":Array.isArray(r)&&(r=T.map(r,function(e){return null==e?"":e+""})),(t=T.valHooks[this.type]||T.valHooks[this.nodeName.toLowerCase()])&&"set"in t&&void 0!==t.set(this,r,"value")||(this.value=r))})):r?(t=T.valHooks[r.type]||T.valHooks[r.nodeName.toLowerCase()])&&"get"in t&&void 0!==(n=t.get(r,"value"))?n:"string"==typeof(n=r.value)?n.replace(Ct,""):null==n?"":n:void 0}}),T.extend({valHooks:{option:{get:function(e){var t=T.find.attr(e,"value");return null!=t?t:Tt(T.text(e))}},select:{get:function(e){var t,n,i,r=e.options,o=e.selectedIndex,a="select-one"===e.type,s=a?null:[],l=a?o+1:r.length;for(i=o<0?l:a?o:0;i<l;i++)if(((n=r[i]).selected||i===o)&&!n.disabled&&(!n.parentNode.disabled||!x(n.parentNode,"optgroup"))){if(t=T(n).val(),a)return t;s.push(t)}return s},set:function(e,t){for(var n,i,r=e.options,o=T.makeArray(t),a=r.length;a--;)((i=r[a]).selected=-1<T.inArray(T.valHooks.option.get(i),o))&&(n=!0);return n||(e.selectedIndex=-1),o}}}}),T.each(["radio","checkbox"],function(){T.valHooks[this]={set:function(e,t){if(Array.isArray(t))return e.checked=-1<T.inArray(T(e).val(),t)}},h.checkOn||(T.valHooks[this].get=function(e){return null===e.getAttribute("value")?"on":e.value})});var St=e.location,kt={guid:Date.now()},At=/\?/;T.parseXML=function(t){var n,i;if(!t||"string"!=typeof t)return null;try{n=(new e.DOMParser).parseFromString(t,"text/xml")}catch(t){}return i=n&&n.getElementsByTagName("parsererror")[0],n&&!i||T.error("Invalid XML: "+(i?T.map(i.childNodes,function(e){return e.textContent}).join("\n"):t)),n};var Dt=/^(?:focusinfocus|focusoutblur)$/,Nt=function(e){e.stopPropagation()};T.extend(T.event,{trigger:function(t,n,i,r){var o,a,s,l,u,f,d,h,v=[i||m],y=c.call(t,"type")?t.type:t,_=c.call(t,"namespace")?t.namespace.split("."):[];if(a=h=s=i=i||m,3!==i.nodeType&&8!==i.nodeType&&!Dt.test(y+T.event.triggered)&&(-1<y.indexOf(".")&&(y=(_=y.split(".")).shift(),_.sort()),u=y.indexOf(":")<0&&"on"+y,(t=t[T.expando]?t:new T.Event(y,"object"==typeof t&&t)).isTrigger=r?2:3,t.namespace=_.join("."),t.rnamespace=t.namespace?new RegExp("(^|\\.)"+_.join("\\.(?:.*\\.|)")+"(\\.|$)"):null,t.result=void 0,t.target||(t.target=i),n=null==n?[t]:T.makeArray(n,[t]),d=T.event.special[y]||{},r||!d.trigger||!1!==d.trigger.apply(i,n))){if(!r&&!d.noBubble&&!g(i)){for(Dt.test((l=d.delegateType||y)+y)||(a=a.parentNode);a;a=a.parentNode)v.push(a),s=a;s===(i.ownerDocument||m)&&v.push(s.defaultView||s.parentWindow||e)}for(o=0;(a=v[o++])&&!t.isPropagationStopped();)h=a,t.type=1<o?l:d.bindType||y,(f=(re.get(a,"events")||Object.create(null))[t.type]&&re.get(a,"handle"))&&f.apply(a,n),(f=u&&a[u])&&f.apply&&ne(a)&&(t.result=f.apply(a,n),!1===t.result&&t.preventDefault());return t.type=y,r||t.isDefaultPrevented()||d._default&&!1!==d._default.apply(v.pop(),n)||!ne(i)||u&&p(i[y])&&!g(i)&&((s=i[u])&&(i[u]=null),T.event.triggered=y,t.isPropagationStopped()&&h.addEventListener(y,Nt),i[y](),t.isPropagationStopped()&&h.removeEventListener(y,Nt),T.event.triggered=void 0,s&&(i[u]=s)),t.result}},simulate:function(e,t,n){var i=T.extend(new T.Event,n,{type:e,isSimulated:!0});T.event.trigger(i,null,t)}}),T.fn.extend({trigger:function(e,t){return this.each(function(){T.event.trigger(e,t,this)})},triggerHandler:function(e,t){var n=this[0];if(n)return T.event.trigger(e,t,n,!0)}});var jt=/\[\]$/,It=/\r?\n/g,Ot=/^(?:submit|button|image|reset|file)$/i,Lt=/^(?:input|select|textarea|keygen)/i;function qt(e,t,n,i){var r;if(Array.isArray(t))T.each(t,function(t,r){n||jt.test(e)?i(e,r):qt(e+"["+("object"==typeof r&&null!=r?t:"")+"]",r,n,i)});else if(n||"object"!==_(t))i(e,t);else for(r in t)qt(e+"["+r+"]",t[r],n,i)}T.param=function(e,t){var n,i=[],r=function(e,t){var n=p(t)?t():t;i[i.length]=encodeURIComponent(e)+"="+encodeURIComponent(null==n?"":n)};if(null==e)return"";if(Array.isArray(e)||e.jquery&&!T.isPlainObject(e))T.each(e,function(){r(this.name,this.value)});else for(n in e)qt(n,e[n],t,r);return i.join("&")},T.fn.extend({serialize:function(){return T.param(this.serializeArray())},serializeArray:function(){return this.map(function(){var e=T.prop(this,"elements");return e?T.makeArray(e):this}).filter(function(){var e=this.type;return this.name&&!T(this).is(":disabled")&&Lt.test(this.nodeName)&&!Ot.test(e)&&(this.checked||!we.test(e))}).map(function(e,t){var n=T(this).val();return null==n?null:Array.isArray(n)?T.map(n,function(e){return{name:t.name,value:e.replace(It,"\r\n")}}):{name:t.name,value:n.replace(It,"\r\n")}}).get()}});var Pt=/%20/g,Rt=/#.*$/,Ht=/([?&])_=[^&]*/,Ft=/^(.*?):[ \t]*([^\r\n]*)$/gm,Mt=/^(?:GET|HEAD)$/,Bt=/^\/\//,Wt={},Ut={},Qt="*/".concat("*"),$t=m.createElement("a");function zt(e){return function(t,n){"string"!=typeof t&&(n=t,t="*");var i,r=0,o=t.toLowerCase().match(Q)||[];if(p(n))for(;i=o[r++];)"+"===i[0]?(i=i.slice(1)||"*",(e[i]=e[i]||[]).unshift(n)):(e[i]=e[i]||[]).push(n)}}function Xt(e,t,n,i){var r={},o=e===Ut;function a(s){var l;return r[s]=!0,T.each(e[s]||[],function(e,s){var u=s(t,n,i);return"string"!=typeof u||o||r[u]?o?!(l=u):void 0:(t.dataTypes.unshift(u),a(u),!1)}),l}return a(t.dataTypes[0])||!r["*"]&&a("*")}function Vt(e,t){var n,i,r=T.ajaxSettings.flatOptions||{};for(n in t)void 0!==t[n]&&((r[n]?e:i||(i={}))[n]=t[n]);return i&&T.extend(!0,e,i),e}$t.href=St.href,T.extend({active:0,lastModified:{},etag:{},ajaxSettings:{url:St.href,type:"GET",isLocal:/^(?:about|app|app-storage|.+-extension|file|res|widget):$/.test(St.protocol),global:!0,processData:!0,async:!0,contentType:"application/x-www-form-urlencoded; charset=UTF-8",accepts:{"*":Qt,text:"text/plain",html:"text/html",xml:"application/xml, text/xml",json:"application/json, text/javascript"},contents:{xml:/\bxml\b/,html:/\bhtml/,json:/\bjson\b/},responseFields:{xml:"responseXML",text:"responseText",json:"responseJSON"},converters:{"* text":String,"text html":!0,"text json":JSON.parse,"text xml":T.parseXML},flatOptions:{url:!0,context:!0}},ajaxSetup:function(e,t){return t?Vt(Vt(e,T.ajaxSettings),t):Vt(T.ajaxSettings,e)},ajaxPrefilter:zt(Wt),ajaxTransport:zt(Ut),ajax:function(t,n){"object"==typeof t&&(n=t,t=void 0);var i,r,o,a,s,l,u,c,f,d,h=T.ajaxSetup({},n=n||{}),p=h.context||h,g=h.context&&(p.nodeType||p.jquery)?T(p):T.event,v=T.Deferred(),y=T.Callbacks("once memory"),_=h.statusCode||{},b={},w={},E="canceled",x={readyState:0,getResponseHeader:function(e){var t;if(u){if(!a)for(a={};t=Ft.exec(o);)a[t[1].toLowerCase()+" "]=(a[t[1].toLowerCase()+" "]||[]).concat(t[2]);t=a[e.toLowerCase()+" "]}return null==t?null:t.join(", ")},getAllResponseHeaders:function(){return u?o:null},setRequestHeader:function(e,t){return null==u&&(e=w[e.toLowerCase()]=w[e.toLowerCase()]||e,b[e]=t),this},overrideMimeType:function(e){return null==u&&(h.mimeType=e),this},statusCode:function(e){var t;if(e)if(u)x.always(e[x.status]);else for(t in e)_[t]=[_[t],e[t]];return this},abort:function(e){var t=e||E;return i&&i.abort(t),C(0,t),this}};if(v.promise(x),h.url=((t||h.url||St.href)+"").replace(Bt,St.protocol+"//"),h.type=n.method||n.type||h.method||h.type,h.dataTypes=(h.dataType||"*").toLowerCase().match(Q)||[""],null==h.crossDomain){l=m.createElement("a");try{l.href=h.url,l.href=l.href,h.crossDomain=$t.protocol+"//"+$t.host!=l.protocol+"//"+l.host}catch(t){h.crossDomain=!0}}if(h.data&&h.processData&&"string"!=typeof h.data&&(h.data=T.param(h.data,h.traditional)),Xt(Wt,h,n,x),u)return x;for(f in(c=T.event&&h.global)&&0==T.active++&&T.event.trigger("ajaxStart"),h.type=h.type.toUpperCase(),h.hasContent=!Mt.test(h.type),r=h.url.replace(Rt,""),h.hasContent?h.data&&h.processData&&0===(h.contentType||"").indexOf("application/x-www-form-urlencoded")&&(h.data=h.data.replace(Pt,"+")):(d=h.url.slice(r.length),h.data&&(h.processData||"string"==typeof h.data)&&(r+=(At.test(r)?"&":"?")+h.data,delete h.data),!1===h.cache&&(r=r.replace(Ht,"$1"),d=(At.test(r)?"&":"?")+"_="+kt.guid+++d),h.url=r+d),h.ifModified&&(T.lastModified[r]&&x.setRequestHeader("If-Modified-Since",T.lastModified[r]),T.etag[r]&&x.setRequestHeader("If-None-Match",T.etag[r])),(h.data&&h.hasContent&&!1!==h.contentType||n.contentType)&&x.setRequestHeader("Content-Type",h.contentType),x.setRequestHeader("Accept",h.dataTypes[0]&&h.accepts[h.dataTypes[0]]?h.accepts[h.dataTypes[0]]+("*"!==h.dataTypes[0]?", "+Qt+"; q=0.01":""):h.accepts["*"]),h.headers)x.setRequestHeader(f,h.headers[f]);if(h.beforeSend&&(!1===h.beforeSend.call(p,x,h)||u))return x.abort();if(E="abort",y.add(h.complete),x.done(h.success),x.fail(h.error),i=Xt(Ut,h,n,x)){if(x.readyState=1,c&&g.trigger("ajaxSend",[x,h]),u)return x;h.async&&0<h.timeout&&(s=e.setTimeout(function(){x.abort("timeout")},h.timeout));try{u=!1,i.send(b,C)}catch(t){if(u)throw t;C(-1,t)}}else C(-1,"No Transport");function C(t,n,a,l){var f,d,m,b,w,E=n;u||(u=!0,s&&e.clearTimeout(s),i=void 0,o=l||"",x.readyState=0<t?4:0,f=200<=t&&t<300||304===t,a&&(b=function(e,t,n){for(var i,r,o,a,s=e.contents,l=e.dataTypes;"*"===l[0];)l.shift(),void 0===i&&(i=e.mimeType||t.getResponseHeader("Content-Type"));if(i)for(r in s)if(s[r]&&s[r].test(i)){l.unshift(r);break}if(l[0]in n)o=l[0];else{for(r in n){if(!l[0]||e.converters[r+" "+l[0]]){o=r;break}a||(a=r)}o=o||a}if(o)return o!==l[0]&&l.unshift(o),n[o]}(h,x,a)),!f&&-1<T.inArray("script",h.dataTypes)&&T.inArray("json",h.dataTypes)<0&&(h.converters["text script"]=function(){}),b=function(e,t,n,i){var r,o,a,s,l,u={},c=e.dataTypes.slice();if(c[1])for(a in e.converters)u[a.toLowerCase()]=e.converters[a];for(o=c.shift();o;)if(e.responseFields[o]&&(n[e.responseFields[o]]=t),!l&&i&&e.dataFilter&&(t=e.dataFilter(t,e.dataType)),l=o,o=c.shift())if("*"===o)o=l;else if("*"!==l&&l!==o){if(!(a=u[l+" "+o]||u["* "+o]))for(r in u)if((s=r.split(" "))[1]===o&&(a=u[l+" "+s[0]]||u["* "+s[0]])){!0===a?a=u[r]:!0!==u[r]&&(o=s[0],c.unshift(s[1]));break}if(!0!==a)if(a&&e.throws)t=a(t);else try{t=a(t)}catch(e){return{state:"parsererror",error:a?e:"No conversion from "+l+" to "+o}}}return{state:"success",data:t}}(h,b,x,f),f?(h.ifModified&&((w=x.getResponseHeader("Last-Modified"))&&(T.lastModified[r]=w),(w=x.getResponseHeader("etag"))&&(T.etag[r]=w)),204===t||"HEAD"===h.type?E="nocontent":304===t?E="notmodified":(E=b.state,d=b.data,f=!(m=b.error))):(m=E,!t&&E||(E="error",t<0&&(t=0))),x.status=t,x.statusText=(n||E)+"",f?v.resolveWith(p,[d,E,x]):v.rejectWith(p,[x,E,m]),x.statusCode(_),_=void 0,c&&g.trigger(f?"ajaxSuccess":"ajaxError",[x,h,f?d:m]),y.fireWith(p,[x,E]),c&&(g.trigger("ajaxComplete",[x,h]),--T.active||T.event.trigger("ajaxStop")))}return x},getJSON:function(e,t,n){return T.get(e,t,n,"json")},getScript:function(e,t){return T.get(e,void 0,t,"script")}}),T.each(["get","post"],function(e,t){T[t]=function(e,n,i,r){return p(n)&&(r=r||i,i=n,n=void 0),T.ajax(T.extend({url:e,type:t,dataType:r,data:n,success:i},T.isPlainObject(e)&&e))}}),T.ajaxPrefilter(function(e){var t;for(t in e.headers)"content-type"===t.toLowerCase()&&(e.contentType=e.headers[t]||"")}),T._evalUrl=function(e,t,n){return T.ajax({url:e,type:"GET",dataType:"script",cache:!0,async:!1,global:!1,converters:{"text script":function(){}},dataFilter:function(e){T.globalEval(e,t,n)}})},T.fn.extend({wrapAll:function(e){var t;return this[0]&&(p(e)&&(e=e.call(this[0])),t=T(e,this[0].ownerDocument).eq(0).clone(!0),this[0].parentNode&&t.insertBefore(this[0]),t.map(function(){for(var e=this;e.firstElementChild;)e=e.firstElementChild;return e}).append(this)),this},wrapInner:function(e){return p(e)?this.each(function(t){T(this).wrapInner(e.call(this,t))}):this.each(function(){var t=T(this),n=t.contents();n.length?n.wrapAll(e):t.append(e)})},wrap:function(e){var t=p(e);return this.each(function(n){T(this).wrapAll(t?e.call(this,n):e)})},unwrap:function(e){return this.parent(e).not("body").each(function(){T(this).replaceWith(this.childNodes)}),this}}),T.expr.pseudos.hidden=function(e){return!T.expr.pseudos.visible(e)},T.expr.pseudos.visible=function(e){return!!(e.offsetWidth||e.offsetHeight||e.getClientRects().length)},T.ajaxSettings.xhr=function(){try{return new e.XMLHttpRequest}catch(t){}};var Yt={0:200,1223:204},Kt=T.ajaxSettings.xhr();h.cors=!!Kt&&"withCredentials"in Kt,h.ajax=Kt=!!Kt,T.ajaxTransport(function(t){var n,i;if(h.cors||Kt&&!t.crossDomain)return{send:function(r,o){var a,s=t.xhr();if(s.open(t.type,t.url,t.async,t.username,t.password),t.xhrFields)for(a in t.xhrFields)s[a]=t.xhrFields[a];for(a in t.mimeType&&s.overrideMimeType&&s.overrideMimeType(t.mimeType),t.crossDomain||r["X-Requested-With"]||(r["X-Requested-With"]="XMLHttpRequest"),r)s.setRequestHeader(a,r[a]);n=function(e){return function(){n&&(n=i=s.onload=s.onerror=s.onabort=s.ontimeout=s.onreadystatechange=null,"abort"===e?s.abort():"error"===e?"number"!=typeof s.status?o(0,"error"):o(s.status,s.statusText):o(Yt[s.status]||s.status,s.statusText,"text"!==(s.responseType||"text")||"string"!=typeof s.responseText?{binary:s.response}:{text:s.responseText},s.getAllResponseHeaders()))}},s.onload=n(),i=s.onerror=s.ontimeout=n("error"),void 0!==s.onabort?s.onabort=i:s.onreadystatechange=function(){4===s.readyState&&e.setTimeout(function(){n&&i()})},n=n("abort");try{s.send(t.hasContent&&t.data||null)}catch(r){if(n)throw r}},abort:function(){n&&n()}}}),T.ajaxPrefilter(function(e){e.crossDomain&&(e.contents.script=!1)}),T.ajaxSetup({accepts:{script:"text/javascript, application/javascript, application/ecmascript, application/x-ecmascript"},contents:{script:/\b(?:java|ecma)script\b/},converters:{"text script":function(e){return T.globalEval(e),e}}}),T.ajaxPrefilter("script",function(e){void 0===e.cache&&(e.cache=!1),e.crossDomain&&(e.type="GET")}),T.ajaxTransport("script",function(e){var t,n;if(e.crossDomain||e.scriptAttrs)return{send:function(i,r){t=T("<script>").attr(e.scriptAttrs||{}).prop({charset:e.scriptCharset,src:e.url}).on("load error",n=function(e){t.remove(),n=null,e&&r("error"===e.type?404:200,e.type)}),m.head.appendChild(t[0])},abort:function(){n&&n()}}});var Gt,Jt=[],Zt=/(=)\?(?=&|$)|\?\?/;T.ajaxSetup({jsonp:"callback",jsonpCallback:function(){var e=Jt.pop()||T.expando+"_"+kt.guid++;return this[e]=!0,e}}),T.ajaxPrefilter("json jsonp",function(t,n,i){var r,o,a,s=!1!==t.jsonp&&(Zt.test(t.url)?"url":"string"==typeof t.data&&0===(t.contentType||"").indexOf("application/x-www-form-urlencoded")&&Zt.test(t.data)&&"data");if(s||"jsonp"===t.dataTypes[0])return r=t.jsonpCallback=p(t.jsonpCallback)?t.jsonpCallback():t.jsonpCallback,s?t[s]=t[s].replace(Zt,"$1"+r):!1!==t.jsonp&&(t.url+=(At.test(t.url)?"&":"?")+t.jsonp+"="+r),t.converters["script json"]=function(){return a||T.error(r+" was not called"),a[0]},t.dataTypes[0]="json",o=e[r],e[r]=function(){a=arguments},i.always(function(){void 0===o?T(e).removeProp(r):e[r]=o,t[r]&&(t.jsonpCallback=n.jsonpCallback,Jt.push(r)),a&&p(o)&&o(a[0]),a=o=void 0}),"script"}),h.createHTMLDocument=((Gt=m.implementation.createHTMLDocument("").body).innerHTML="<form></form><form></form>",2===Gt.childNodes.length),T.parseHTML=function(e,t,n){return"string"!=typeof e?[]:("boolean"==typeof t&&(n=t,t=!1),t||(h.createHTMLDocument?((i=(t=m.implementation.createHTMLDocument("")).createElement("base")).href=m.location.href,t.head.appendChild(i)):t=m),o=!n&&[],(r=R.exec(e))?[t.createElement(r[1])]:(r=Ae([e],t,o),o&&o.length&&T(o).remove(),T.merge([],r.childNodes)));var i,r,o},T.fn.load=function(e,t,n){var i,r,o,a=this,s=e.indexOf(" ");return-1<s&&(i=Tt(e.slice(s)),e=e.slice(0,s)),p(t)?(n=t,t=void 0):t&&"object"==typeof t&&(r="POST"),0<a.length&&T.ajax({url:e,type:r||"GET",dataType:"html",data:t}).done(function(e){o=arguments,a.html(i?T("<div>").append(T.parseHTML(e)).find(i):e)}).always(n&&function(e,t){a.each(function(){n.apply(this,o||[e.responseText,t,e])})}),this},T.expr.pseudos.animated=function(e){return T.grep(T.timers,function(t){return e===t.elem}).length},T.offset={setOffset:function(e,t,n){var i,r,o,a,s,l,u=T.css(e,"position"),c=T(e),f={};"static"===u&&(e.style.position="relative"),s=c.offset(),o=T.css(e,"top"),l=T.css(e,"left"),("absolute"===u||"fixed"===u)&&-1<(o+l).indexOf("auto")?(a=(i=c.position()).top,r=i.left):(a=parseFloat(o)||0,r=parseFloat(l)||0),p(t)&&(t=t.call(e,n,T.extend({},s))),null!=t.top&&(f.top=t.top-s.top+a),null!=t.left&&(f.left=t.left-s.left+r),"using"in t?t.using.call(e,f):c.css(f)}},T.fn.extend({offset:function(e){if(arguments.length)return void 0===e?this:this.each(function(t){T.offset.setOffset(this,e,t)});var t,n,i=this[0];return i?i.getClientRects().length?{top:(t=i.getBoundingClientRect()).top+(n=i.ownerDocument.defaultView).pageYOffset,left:t.left+n.pageXOffset}:{top:0,left:0}:void 0},position:function(){if(this[0]){var e,t,n,i=this[0],r={top:0,left:0};if("fixed"===T.css(i,"position"))t=i.getBoundingClientRect();else{for(t=this.offset(),n=i.ownerDocument,e=i.offsetParent||n.documentElement;e&&(e===n.body||e===n.documentElement)&&"static"===T.css(e,"position");)e=e.parentNode;e&&e!==i&&1===e.nodeType&&((r=T(e).offset()).top+=T.css(e,"borderTopWidth",!0),r.left+=T.css(e,"borderLeftWidth",!0))}return{top:t.top-r.top-T.css(i,"marginTop",!0),left:t.left-r.left-T.css(i,"marginLeft",!0)}}},offsetParent:function(){return this.map(function(){for(var e=this.offsetParent;e&&"static"===T.css(e,"position");)e=e.offsetParent;return e||de})}}),T.each({scrollLeft:"pageXOffset",scrollTop:"pageYOffset"},function(e,t){var n="pageYOffset"===t;T.fn[e]=function(i){return G(this,function(e,i,r){var o;if(g(e)?o=e:9===e.nodeType&&(o=e.defaultView),void 0===r)return o?o[t]:e[i];o?o.scrollTo(n?o.pageXOffset:r,n?r:o.pageYOffset):e[i]=r},e,i,arguments.length)}}),T.each(["top","left"],function(e,t){T.cssHooks[t]=Ye(h.pixelPosition,function(e,n){if(n)return n=Ve(e,t),Ue.test(n)?T(e).position()[t]+"px":n})}),T.each({Height:"height",Width:"width"},function(e,t){T.each({padding:"inner"+e,content:t,"":"outer"+e},function(n,i){T.fn[i]=function(r,o){var a=arguments.length&&(n||"boolean"!=typeof r),s=n||(!0===r||!0===o?"margin":"border");return G(this,function(t,n,r){var o;return g(t)?0===i.indexOf("outer")?t["inner"+e]:t.document.documentElement["client"+e]:9===t.nodeType?(o=t.documentElement,Math.max(t.body["scroll"+e],o["scroll"+e],t.body["offset"+e],o["offset"+e],o["client"+e])):void 0===r?T.css(t,n,s):T.style(t,n,r,s)},t,a?r:void 0,a)}})}),T.each(["ajaxStart","ajaxStop","ajaxComplete","ajaxError","ajaxSuccess","ajaxSend"],function(e,t){T.fn[t]=function(e){return this.on(t,e)}}),T.fn.extend({bind:function(e,t,n){return this.on(e,null,t,n)},unbind:function(e,t){return this.off(e,null,t)},delegate:function(e,t,n,i){return this.on(t,e,n,i)},undelegate:function(e,t,n){return 1===arguments.length?this.off(e,"**"):this.off(t,e||"**",n)},hover:function(e,t){return this.on("mouseenter",e).on("mouseleave",t||e)}}),T.each("blur focus focusin focusout resize scroll click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave change select submit keydown keypress keyup contextmenu".split(" "),function(e,t){T.fn[t]=function(e,n){return 0<arguments.length?this.on(t,null,e,n):this.trigger(t)}});var en=/^[\s\uFEFF\xA0]+|([^\s\uFEFF\xA0])[\s\uFEFF\xA0]+$/g;T.proxy=function(e,t){var n,i,o;if("string"==typeof t&&(n=e[t],t=e,e=n),p(e))return i=r.call(arguments,2),(o=function(){return e.apply(t||this,i.concat(r.call(arguments)))}).guid=e.guid=e.guid||T.guid++,o},T.holdReady=function(e){e?T.readyWait++:T.ready(!0)},T.isArray=Array.isArray,T.parseJSON=JSON.parse,T.nodeName=x,T.isFunction=p,T.isWindow=g,T.camelCase=te,T.type=_,T.now=Date.now,T.isNumeric=function(e){var t=T.type(e);return("number"===t||"string"===t)&&!isNaN(e-parseFloat(e))},T.trim=function(e){return null==e?"":(e+"").replace(en,"$1")},"function"==typeof define&&define.amd&&define("jquery",[],function(){return T});var tn=e.jQuery,nn=e.$;return T.noConflict=function(t){return e.$===T&&(e.$=nn),t&&e.jQuery===T&&(e.jQuery=tn),T},void 0===t&&(e.jQuery=e.$=T),T}),function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("jquery"),require("popper.js")):"function"==typeof define&&define.amd?define(["exports","jquery","popper.js"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).bootstrap={},e.jQuery,e.Popper)}(this,function(e,t,n){"use strict";function i(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var r=i(t),o=i(n);function a(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}function s(e,t,n){return t&&a(e.prototype,t),n&&a(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}function l(){return(l=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}function u(e,t){return(u=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}var c="transitionend",f={TRANSITION_END:"bsTransitionEnd",getUID:function(e){do{e+=~~(1e6*Math.random())}while(document.getElementById(e));return e},getSelectorFromElement:function(e){var t=e.getAttribute("data-target");if(!t||"#"===t){var n=e.getAttribute("href");t=n&&"#"!==n?n.trim():""}try{return document.querySelector(t)?t:null}catch(e){return null}},getTransitionDurationFromElement:function(e){if(!e)return 0;var t=r.default(e).css("transition-duration"),n=r.default(e).css("transition-delay"),i=parseFloat(t),o=parseFloat(n);return i||o?(t=t.split(",")[0],n=n.split(",")[0],1e3*(parseFloat(t)+parseFloat(n))):0},reflow:function(e){return e.offsetHeight},triggerTransitionEnd:function(e){r.default(e).trigger(c)},supportsTransitionEnd:function(){return Boolean(c)},isElement:function(e){return(e[0]||e).nodeType},typeCheckConfig:function(e,t,n){for(var i in n)if(Object.prototype.hasOwnProperty.call(n,i)){var r=n[i],o=t[i],a=o&&f.isElement(o)?"element":null===(s=o)||void 0===s?""+s:{}.toString.call(s).match(/\s([a-z]+)/i)[1].toLowerCase();if(!new RegExp(r).test(a))throw new Error(e.toUpperCase()+': Option "'+i+'" provided type "'+a+'" but expected type "'+r+'".')}var s},findShadowRoot:function(e){if(!document.documentElement.attachShadow)return null;if("function"==typeof e.getRootNode){var t=e.getRootNode();return t instanceof ShadowRoot?t:null}return e instanceof ShadowRoot?e:e.parentNode?f.findShadowRoot(e.parentNode):null},jQueryDetection:function(){if(void 0===r.default)throw new TypeError("Bootstrap's JavaScript requires jQuery. jQuery must be included before Bootstrap's JavaScript.");var e=r.default.fn.jquery.split(" ")[0].split(".");if(e[0]<2&&e[1]<9||1===e[0]&&9===e[1]&&e[2]<1||e[0]>=4)throw new Error("Bootstrap's JavaScript requires at least jQuery v1.9.1 but less than v4.0.0")}};f.jQueryDetection(),r.default.fn.emulateTransitionEnd=function(e){var t=this,n=!1;return r.default(this).one(f.TRANSITION_END,function(){n=!0}),setTimeout(function(){n||f.triggerTransitionEnd(t)},e),this},r.default.event.special[f.TRANSITION_END]={bindType:c,delegateType:c,handle:function(e){if(r.default(e.target).is(this))return e.handleObj.handler.apply(this,arguments)}};var d="bs.alert",h=r.default.fn.alert,p=function(){function e(e){this._element=e}var t=e.prototype;return t.close=function(e){var t=this._element;e&&(t=this._getRootElement(e)),this._triggerCloseEvent(t).isDefaultPrevented()||this._removeElement(t)},t.dispose=function(){r.default.removeData(this._element,d),this._element=null},t._getRootElement=function(e){var t=f.getSelectorFromElement(e),n=!1;return t&&(n=document.querySelector(t)),n||(n=r.default(e).closest(".alert")[0]),n},t._triggerCloseEvent=function(e){var t=r.default.Event("close.bs.alert");return r.default(e).trigger(t),t},t._removeElement=function(e){var t=this;if(r.default(e).removeClass("show"),r.default(e).hasClass("fade")){var n=f.getTransitionDurationFromElement(e);r.default(e).one(f.TRANSITION_END,function(n){return t._destroyElement(e,n)}).emulateTransitionEnd(n)}else this._destroyElement(e)},t._destroyElement=function(e){r.default(e).detach().trigger("closed.bs.alert").remove()},e._jQueryInterface=function(t){return this.each(function(){var n=r.default(this),i=n.data(d);i||(i=new e(this),n.data(d,i)),"close"===t&&i[t](this)})},e._handleDismiss=function(e){return function(t){t&&t.preventDefault(),e.close(this)}},s(e,null,[{key:"VERSION",get:function(){return"4.6.2"}}]),e}();r.default(document).on("click.bs.alert.data-api",'[data-dismiss="alert"]',p._handleDismiss(new p)),r.default.fn.alert=p._jQueryInterface,r.default.fn.alert.Constructor=p,r.default.fn.alert.noConflict=function(){return r.default.fn.alert=h,p._jQueryInterface};var g="bs.button",m=r.default.fn.button,v="active",y='[data-toggle^="button"]',_='input:not([type="hidden"])',b=".btn",w=function(){function e(e){this._element=e,this.shouldAvoidTriggerChange=!1}var t=e.prototype;return t.toggle=function(){var e=!0,t=!0,n=r.default(this._element).closest('[data-toggle="buttons"]')[0];if(n){var i=this._element.querySelector(_);if(i){if("radio"===i.type)if(i.checked&&this._element.classList.contains(v))e=!1;else{var o=n.querySelector(".active");o&&r.default(o).removeClass(v)}e&&("checkbox"!==i.type&&"radio"!==i.type||(i.checked=!this._element.classList.contains(v)),this.shouldAvoidTriggerChange||r.default(i).trigger("change")),i.focus(),t=!1}}this._element.hasAttribute("disabled")||this._element.classList.contains("disabled")||(t&&this._element.setAttribute("aria-pressed",!this._element.classList.contains(v)),e&&r.default(this._element).toggleClass(v))},t.dispose=function(){r.default.removeData(this._element,g),this._element=null},e._jQueryInterface=function(t,n){return this.each(function(){var i=r.default(this),o=i.data(g);o||(o=new e(this),i.data(g,o)),o.shouldAvoidTriggerChange=n,"toggle"===t&&o[t]()})},s(e,null,[{key:"VERSION",get:function(){return"4.6.2"}}]),e}();r.default(document).on("click.bs.button.data-api",y,function(e){var t=e.target,n=t;if(r.default(t).hasClass("btn")||(t=r.default(t).closest(b)[0]),!t||t.hasAttribute("disabled")||t.classList.contains("disabled"))e.preventDefault();else{var i=t.querySelector(_);if(i&&(i.hasAttribute("disabled")||i.classList.contains("disabled")))return void e.preventDefault();"INPUT"!==n.tagName&&"LABEL"===t.tagName||w._jQueryInterface.call(r.default(t),"toggle","INPUT"===n.tagName)}}).on("focus.bs.button.data-api blur.bs.button.data-api",y,function(e){var t=r.default(e.target).closest(b)[0];r.default(t).toggleClass("focus",/^focus(in)?$/.test(e.type))}),r.default(window).on("load.bs.button.data-api",function(){for(var e=[].slice.call(document.querySelectorAll('[data-toggle="buttons"] .btn')),t=0,n=e.length;t<n;t++){var i=e[t],r=i.querySelector(_);r.checked||r.hasAttribute("checked")?i.classList.add(v):i.classList.remove(v)}for(var o=0,a=(e=[].slice.call(document.querySelectorAll('[data-toggle="button"]'))).length;o<a;o++){var s=e[o];"true"===s.getAttribute("aria-pressed")?s.classList.add(v):s.classList.remove(v)}}),r.default.fn.button=w._jQueryInterface,r.default.fn.button.Constructor=w,r.default.fn.button.noConflict=function(){return r.default.fn.button=m,w._jQueryInterface};var T="carousel",E="bs.carousel",x=r.default.fn[T],C="active",S="next",k="prev",A="slid.bs.carousel",D=".active.carousel-item",N={interval:5e3,keyboard:!0,slide:!1,pause:"hover",wrap:!0,touch:!0},j={interval:"(number|boolean)",keyboard:"boolean",slide:"(boolean|string)",pause:"(string|boolean)",wrap:"boolean",touch:"boolean"},I={TOUCH:"touch",PEN:"pen"},O=function(){function e(e,t){this._items=null,this._interval=null,this._activeElement=null,this._isPaused=!1,this._isSliding=!1,this.touchTimeout=null,this.touchStartX=0,this.touchDeltaX=0,this._config=this._getConfig(t),this._element=e,this._indicatorsElement=this._element.querySelector(".carousel-indicators"),this._touchSupported="ontouchstart"in document.documentElement||navigator.maxTouchPoints>0,this._pointerEvent=Boolean(window.PointerEvent||window.MSPointerEvent),this._addEventListeners()}var t=e.prototype;return t.next=function(){this._isSliding||this._slide(S)},t.nextWhenVisible=function(){var e=r.default(this._element);!document.hidden&&e.is(":visible")&&"hidden"!==e.css("visibility")&&this.next()},t.prev=function(){this._isSliding||this._slide(k)},t.pause=function(e){e||(this._isPaused=!0),this._element.querySelector(".carousel-item-next, .carousel-item-prev")&&(f.triggerTransitionEnd(this._element),this.cycle(!0)),clearInterval(this._interval),this._interval=null},t.cycle=function(e){e||(this._isPaused=!1),this._interval&&(clearInterval(this._interval),this._interval=null),this._config.interval&&!this._isPaused&&(this._updateInterval(),this._interval=setInterval((document.visibilityState?this.nextWhenVisible:this.next).bind(this),this._config.interval))},t.to=function(e){var t=this;this._activeElement=this._element.querySelector(D);var n=this._getItemIndex(this._activeElement);if(!(e>this._items.length-1||e<0))if(this._isSliding)r.default(this._element).one(A,function(){return t.to(e)});else{if(n===e)return this.pause(),void this.cycle();this._slide(e>n?S:k,this._items[e])}},t.dispose=function(){r.default(this._element).off(".bs.carousel"),r.default.removeData(this._element,E),this._items=null,this._config=null,this._element=null,this._interval=null,this._isPaused=null,this._isSliding=null,this._activeElement=null,this._indicatorsElement=null},t._getConfig=function(e){return e=l({},N,e),f.typeCheckConfig(T,e,j),e},t._handleSwipe=function(){var e=Math.abs(this.touchDeltaX);if(!(e<=40)){var t=e/this.touchDeltaX;this.touchDeltaX=0,t>0&&this.prev(),t<0&&this.next()}},t._addEventListeners=function(){var e=this;this._config.keyboard&&r.default(this._element).on("keydown.bs.carousel",function(t){return e._keydown(t)}),"hover"===this._config.pause&&r.default(this._element).on("mouseenter.bs.carousel",function(t){return e.pause(t)}).on("mouseleave.bs.carousel",function(t){return e.cycle(t)}),this._config.touch&&this._addTouchEventListeners()},t._addTouchEventListeners=function(){var e=this;if(this._touchSupported){var t=function(t){e._pointerEvent&&I[t.originalEvent.pointerType.toUpperCase()]?e.touchStartX=t.originalEvent.clientX:e._pointerEvent||(e.touchStartX=t.originalEvent.touches[0].clientX)},n=function(t){e._pointerEvent&&I[t.originalEvent.pointerType.toUpperCase()]&&(e.touchDeltaX=t.originalEvent.clientX-e.touchStartX),e._handleSwipe(),"hover"===e._config.pause&&(e.pause(),e.touchTimeout&&clearTimeout(e.touchTimeout),e.touchTimeout=setTimeout(function(t){return e.cycle(t)},500+e._config.interval))};r.default(this._element.querySelectorAll(".carousel-item img")).on("dragstart.bs.carousel",function(e){return e.preventDefault()}),this._pointerEvent?(r.default(this._element).on("pointerdown.bs.carousel",function(e){return t(e)}),r.default(this._element).on("pointerup.bs.carousel",function(e){return n(e)}),this._element.classList.add("pointer-event")):(r.default(this._element).on("touchstart.bs.carousel",function(e){return t(e)}),r.default(this._element).on("touchmove.bs.carousel",function(t){return function(t){e.touchDeltaX=t.originalEvent.touches&&t.originalEvent.touches.length>1?0:t.originalEvent.touches[0].clientX-e.touchStartX}(t)}),r.default(this._element).on("touchend.bs.carousel",function(e){return n(e)}))}},t._keydown=function(e){if(!/input|textarea/i.test(e.target.tagName))switch(e.which){case 37:e.preventDefault(),this.prev();break;case 39:e.preventDefault(),this.next()}},t._getItemIndex=function(e){return this._items=e&&e.parentNode?[].slice.call(e.parentNode.querySelectorAll(".carousel-item")):[],this._items.indexOf(e)},t._getItemByDirection=function(e,t){var n=e===S,i=e===k,r=this._getItemIndex(t);if((i&&0===r||n&&r===this._items.length-1)&&!this._config.wrap)return t;var o=(r+(e===k?-1:1))%this._items.length;return-1===o?this._items[this._items.length-1]:this._items[o]},t._triggerSlideEvent=function(e,t){var n=this._getItemIndex(e),i=this._getItemIndex(this._element.querySelector(D)),o=r.default.Event("slide.bs.carousel",{relatedTarget:e,direction:t,from:i,to:n});return r.default(this._element).trigger(o),o},t._setActiveIndicatorElement=function(e){if(this._indicatorsElement){var t=[].slice.call(this._indicatorsElement.querySelectorAll(".active"));r.default(t).removeClass(C);var n=this._indicatorsElement.children[this._getItemIndex(e)];n&&r.default(n).addClass(C)}},t._updateInterval=function(){var e=this._activeElement||this._element.querySelector(D);if(e){var t=parseInt(e.getAttribute("data-interval"),10);t?(this._config.defaultInterval=this._config.defaultInterval||this._config.interval,this._config.interval=t):this._config.interval=this._config.defaultInterval||this._config.interval}},t._slide=function(e,t){var n,i,o,a=this,s=this._element.querySelector(D),l=this._getItemIndex(s),u=t||s&&this._getItemByDirection(e,s),c=this._getItemIndex(u),d=Boolean(this._interval);if(e===S?(n="carousel-item-left",i="carousel-item-next",o="left"):(n="carousel-item-right",i="carousel-item-prev",o="right"),u&&r.default(u).hasClass(C))this._isSliding=!1;else if(!this._triggerSlideEvent(u,o).isDefaultPrevented()&&s&&u){this._isSliding=!0,d&&this.pause(),this._setActiveIndicatorElement(u),this._activeElement=u;var h=r.default.Event(A,{relatedTarget:u,direction:o,from:l,to:c});if(r.default(this._element).hasClass("slide")){r.default(u).addClass(i),f.reflow(u),r.default(s).addClass(n),r.default(u).addClass(n);var p=f.getTransitionDurationFromElement(s);r.default(s).one(f.TRANSITION_END,function(){r.default(u).removeClass(n+" "+i).addClass(C),r.default(s).removeClass("active "+i+" "+n),a._isSliding=!1,setTimeout(function(){return r.default(a._element).trigger(h)},0)}).emulateTransitionEnd(p)}else r.default(s).removeClass(C),r.default(u).addClass(C),this._isSliding=!1,r.default(this._element).trigger(h);d&&this.cycle()}},e._jQueryInterface=function(t){return this.each(function(){var n=r.default(this).data(E),i=l({},N,r.default(this).data());"object"==typeof t&&(i=l({},i,t));var o="string"==typeof t?t:i.slide;if(n||(n=new e(this,i),r.default(this).data(E,n)),"number"==typeof t)n.to(t);else if("string"==typeof o){if(void 0===n[o])throw new TypeError('No method named "'+o+'"');n[o]()}else i.interval&&i.ride&&(n.pause(),n.cycle())})},e._dataApiClickHandler=function(t){var n=f.getSelectorFromElement(this);if(n){var i=r.default(n)[0];if(i&&r.default(i).hasClass("carousel")){var o=l({},r.default(i).data(),r.default(this).data()),a=this.getAttribute("data-slide-to");a&&(o.interval=!1),e._jQueryInterface.call(r.default(i),o),a&&r.default(i).data(E).to(a),t.preventDefault()}}},s(e,null,[{key:"VERSION",get:function(){return"4.6.2"}},{key:"Default",get:function(){return N}}]),e}();r.default(document).on("click.bs.carousel.data-api","[data-slide], [data-slide-to]",O._dataApiClickHandler),r.default(window).on("load.bs.carousel.data-api",function(){for(var e=[].slice.call(document.querySelectorAll('[data-ride="carousel"]')),t=0,n=e.length;t<n;t++){var i=r.default(e[t]);O._jQueryInterface.call(i,i.data())}}),r.default.fn[T]=O._jQueryInterface,r.default.fn[T].Constructor=O,r.default.fn[T].noConflict=function(){return r.default.fn[T]=x,O._jQueryInterface};var L="collapse",q="bs.collapse",P=r.default.fn[L],R="show",H="collapse",F="collapsing",M="collapsed",B="width",W='[data-toggle="collapse"]',U={toggle:!0,parent:""},Q={toggle:"boolean",parent:"(string|element)"},$=function(){function e(e,t){this._isTransitioning=!1,this._element=e,this._config=this._getConfig(t),this._triggerArray=[].slice.call(document.querySelectorAll('[data-toggle="collapse"][href="#'+e.id+'"],[data-toggle="collapse"][data-target="#'+e.id+'"]'));for(var n=[].slice.call(document.querySelectorAll(W)),i=0,r=n.length;i<r;i++){var o=n[i],a=f.getSelectorFromElement(o),s=[].slice.call(document.querySelectorAll(a)).filter(function(t){return t===e});null!==a&&s.length>0&&(this._selector=a,this._triggerArray.push(o))}this._parent=this._config.parent?this._getParent():null,this._config.parent||this._addAriaAndCollapsedClass(this._element,this._triggerArray),this._config.toggle&&this.toggle()}var t=e.prototype;return t.toggle=function(){r.default(this._element).hasClass(R)?this.hide():this.show()},t.show=function(){var t,n,i=this;if(!(this._isTransitioning||r.default(this._element).hasClass(R)||(this._parent&&0===(t=[].slice.call(this._parent.querySelectorAll(".show, .collapsing")).filter(function(e){return"string"==typeof i._config.parent?e.getAttribute("data-parent")===i._config.parent:e.classList.contains(H)})).length&&(t=null),t&&(n=r.default(t).not(this._selector).data(q))&&n._isTransitioning))){var o=r.default.Event("show.bs.collapse");if(r.default(this._element).trigger(o),!o.isDefaultPrevented()){t&&(e._jQueryInterface.call(r.default(t).not(this._selector),"hide"),n||r.default(t).data(q,null));var a=this._getDimension();r.default(this._element).removeClass(H).addClass(F),this._element.style[a]=0,this._triggerArray.length&&r.default(this._triggerArray).removeClass(M).attr("aria-expanded",!0),this.setTransitioning(!0);var s="scroll"+(a[0].toUpperCase()+a.slice(1)),l=f.getTransitionDurationFromElement(this._element);r.default(this._element).one(f.TRANSITION_END,function(){r.default(i._element).removeClass(F).addClass("collapse show"),i._element.style[a]="",i.setTransitioning(!1),r.default(i._element).trigger("shown.bs.collapse")}).emulateTransitionEnd(l),this._element.style[a]=this._element[s]+"px"}}},t.hide=function(){var e=this;if(!this._isTransitioning&&r.default(this._element).hasClass(R)){var t=r.default.Event("hide.bs.collapse");if(r.default(this._element).trigger(t),!t.isDefaultPrevented()){var n=this._getDimension();this._element.style[n]=this._element.getBoundingClientRect()[n]+"px",f.reflow(this._element),r.default(this._element).addClass(F).removeClass("collapse show");var i=this._triggerArray.length;if(i>0)for(var o=0;o<i;o++){var a=this._triggerArray[o],s=f.getSelectorFromElement(a);null!==s&&(r.default([].slice.call(document.querySelectorAll(s))).hasClass(R)||r.default(a).addClass(M).attr("aria-expanded",!1))}this.setTransitioning(!0),this._element.style[n]="";var l=f.getTransitionDurationFromElement(this._element);r.default(this._element).one(f.TRANSITION_END,function(){e.setTransitioning(!1),r.default(e._element).removeClass(F).addClass(H).trigger("hidden.bs.collapse")}).emulateTransitionEnd(l)}}},t.setTransitioning=function(e){this._isTransitioning=e},t.dispose=function(){r.default.removeData(this._element,q),this._config=null,this._parent=null,this._element=null,this._triggerArray=null,this._isTransitioning=null},t._getConfig=function(e){return(e=l({},U,e)).toggle=Boolean(e.toggle),f.typeCheckConfig(L,e,Q),e},t._getDimension=function(){return r.default(this._element).hasClass(B)?B:"height"},t._getParent=function(){var t,n=this;f.isElement(this._config.parent)?(t=this._config.parent,void 0!==this._config.parent.jquery&&(t=this._config.parent[0])):t=document.querySelector(this._config.parent);var i=[].slice.call(t.querySelectorAll('[data-toggle="collapse"][data-parent="'+this._config.parent+'"]'));return r.default(i).each(function(t,i){n._addAriaAndCollapsedClass(e._getTargetFromElement(i),[i])}),t},t._addAriaAndCollapsedClass=function(e,t){var n=r.default(e).hasClass(R);t.length&&r.default(t).toggleClass(M,!n).attr("aria-expanded",n)},e._getTargetFromElement=function(e){var t=f.getSelectorFromElement(e);return t?document.querySelector(t):null},e._jQueryInterface=function(t){return this.each(function(){var n=r.default(this),i=n.data(q),o=l({},U,n.data(),"object"==typeof t&&t?t:{});if(!i&&o.toggle&&"string"==typeof t&&/show|hide/.test(t)&&(o.toggle=!1),i||(i=new e(this,o),n.data(q,i)),"string"==typeof t){if(void 0===i[t])throw new TypeError('No method named "'+t+'"');i[t]()}})},s(e,null,[{key:"VERSION",get:function(){return"4.6.2"}},{key:"Default",get:function(){return U}}]),e}();r.default(document).on("click.bs.collapse.data-api",W,function(e){"A"===e.currentTarget.tagName&&e.preventDefault();var t=r.default(this),n=f.getSelectorFromElement(this),i=[].slice.call(document.querySelectorAll(n));r.default(i).each(function(){var e=r.default(this),n=e.data(q)?"toggle":t.data();$._jQueryInterface.call(e,n)})}),r.default.fn[L]=$._jQueryInterface,r.default.fn[L].Constructor=$,r.default.fn[L].noConflict=function(){return r.default.fn[L]=P,$._jQueryInterface};var z="dropdown",X="bs.dropdown",V=r.default.fn[z],Y=new RegExp("38|40|27"),K="disabled",G="show",J="dropdown-menu-right",Z="hide.bs.dropdown",ee="hidden.bs.dropdown",te="click.bs.dropdown.data-api",ne="keydown.bs.dropdown.data-api",ie='[data-toggle="dropdown"]',re=".dropdown-menu",oe={offset:0,flip:!0,boundary:"scrollParent",reference:"toggle",display:"dynamic",popperConfig:null},ae={offset:"(number|string|function)",flip:"boolean",boundary:"(string|element)",reference:"(string|element)",display:"string",popperConfig:"(null|object)"},se=function(){function e(e,t){this._element=e,this._popper=null,this._config=this._getConfig(t),this._menu=this._getMenuElement(),this._inNavbar=this._detectNavbar(),this._addEventListeners()}var t=e.prototype;return t.toggle=function(){if(!this._element.disabled&&!r.default(this._element).hasClass(K)){var t=r.default(this._menu).hasClass(G);e._clearMenus(),t||this.show(!0)}},t.show=function(t){if(void 0===t&&(t=!1),!(this._element.disabled||r.default(this._element).hasClass(K)||r.default(this._menu).hasClass(G))){var n={relatedTarget:this._element},i=r.default.Event("show.bs.dropdown",n),a=e._getParentFromElement(this._element);if(r.default(a).trigger(i),!i.isDefaultPrevented()){if(!this._inNavbar&&t){if(void 0===o.default)throw new TypeError("Bootstrap's dropdowns require Popper (https://popper.js.org)");var s=this._element;"parent"===this._config.reference?s=a:f.isElement(this._config.reference)&&(s=this._config.reference,void 0!==this._config.reference.jquery&&(s=this._config.reference[0])),"scrollParent"!==this._config.boundary&&r.default(a).addClass("position-static"),this._popper=new o.default(s,this._menu,this._getPopperConfig())}"ontouchstart"in document.documentElement&&0===r.default(a).closest(".navbar-nav").length&&r.default(document.body).children().on("mouseover",null,r.default.noop),this._element.focus(),this._element.setAttribute("aria-expanded",!0),r.default(this._menu).toggleClass(G),r.default(a).toggleClass(G).trigger(r.default.Event("shown.bs.dropdown",n))}}},t.hide=function(){if(!this._element.disabled&&!r.default(this._element).hasClass(K)&&r.default(this._menu).hasClass(G)){var t={relatedTarget:this._element},n=r.default.Event(Z,t),i=e._getParentFromElement(this._element);r.default(i).trigger(n),n.isDefaultPrevented()||(this._popper&&this._popper.destroy(),r.default(this._menu).toggleClass(G),r.default(i).toggleClass(G).trigger(r.default.Event(ee,t)))}},t.dispose=function(){r.default.removeData(this._element,X),r.default(this._element).off(".bs.dropdown"),this._element=null,this._menu=null,null!==this._popper&&(this._popper.destroy(),this._popper=null)},t.update=function(){this._inNavbar=this._detectNavbar(),null!==this._popper&&this._popper.scheduleUpdate()},t._addEventListeners=function(){var e=this;r.default(this._element).on("click.bs.dropdown",function(t){t.preventDefault(),t.stopPropagation(),e.toggle()})},t._getConfig=function(e){return e=l({},this.constructor.Default,r.default(this._element).data(),e),f.typeCheckConfig(z,e,this.constructor.DefaultType),e},t._getMenuElement=function(){if(!this._menu){var t=e._getParentFromElement(this._element);t&&(this._menu=t.querySelector(re))}return this._menu},t._getPlacement=function(){var e=r.default(this._element.parentNode),t="bottom-start";return e.hasClass("dropup")?t=r.default(this._menu).hasClass(J)?"top-end":"top-start":e.hasClass("dropright")?t="right-start":e.hasClass("dropleft")?t="left-start":r.default(this._menu).hasClass(J)&&(t="bottom-end"),t},t._detectNavbar=function(){return r.default(this._element).closest(".navbar").length>0},t._getOffset=function(){var e=this,t={};return"function"==typeof this._config.offset?t.fn=function(t){return t.offsets=l({},t.offsets,e._config.offset(t.offsets,e._element)),t}:t.offset=this._config.offset,t},t._getPopperConfig=function(){var e={placement:this._getPlacement(),modifiers:{offset:this._getOffset(),flip:{enabled:this._config.flip},preventOverflow:{boundariesElement:this._config.boundary}}};return"static"===this._config.display&&(e.modifiers.applyStyle={enabled:!1}),l({},e,this._config.popperConfig)},e._jQueryInterface=function(t){return this.each(function(){var n=r.default(this).data(X);if(n||(n=new e(this,"object"==typeof t?t:null),r.default(this).data(X,n)),"string"==typeof t){if(void 0===n[t])throw new TypeError('No method named "'+t+'"');n[t]()}})},e._clearMenus=function(t){if(!t||3!==t.which&&("keyup"!==t.type||9===t.which))for(var n=[].slice.call(document.querySelectorAll(ie)),i=0,o=n.length;i<o;i++){var a=e._getParentFromElement(n[i]),s=r.default(n[i]).data(X),l={relatedTarget:n[i]};if(t&&"click"===t.type&&(l.clickEvent=t),s){var u=s._menu;if(r.default(a).hasClass(G)&&!(t&&("click"===t.type&&/input|textarea/i.test(t.target.tagName)||"keyup"===t.type&&9===t.which)&&r.default.contains(a,t.target))){var c=r.default.Event(Z,l);r.default(a).trigger(c),c.isDefaultPrevented()||("ontouchstart"in document.documentElement&&r.default(document.body).children().off("mouseover",null,r.default.noop),n[i].setAttribute("aria-expanded","false"),s._popper&&s._popper.destroy(),r.default(u).removeClass(G),r.default(a).removeClass(G).trigger(r.default.Event(ee,l)))}}}},e._getParentFromElement=function(e){var t,n=f.getSelectorFromElement(e);return n&&(t=document.querySelector(n)),t||e.parentNode},e._dataApiKeydownHandler=function(t){if(!(/input|textarea/i.test(t.target.tagName)?32===t.which||27!==t.which&&(40!==t.which&&38!==t.which||r.default(t.target).closest(re).length):!Y.test(t.which))&&!this.disabled&&!r.default(this).hasClass(K)){var n=e._getParentFromElement(this),i=r.default(n).hasClass(G);if(i||27!==t.which){if(t.preventDefault(),t.stopPropagation(),!i||27===t.which||32===t.which)return 27===t.which&&r.default(n.querySelector(ie)).trigger("focus"),void r.default(this).trigger("click");var o=[].slice.call(n.querySelectorAll(".dropdown-menu .dropdown-item:not(.disabled):not(:disabled)")).filter(function(e){return r.default(e).is(":visible")});if(0!==o.length){var a=o.indexOf(t.target);38===t.which&&a>0&&a--,40===t.which&&a<o.length-1&&a++,a<0&&(a=0),o[a].focus()}}}},s(e,null,[{key:"VERSION",get:function(){return"4.6.2"}},{key:"Default",get:function(){return oe}},{key:"DefaultType",get:function(){return ae}}]),e}();r.default(document).on(ne,ie,se._dataApiKeydownHandler).on(ne,re,se._dataApiKeydownHandler).on(te+" keyup.bs.dropdown.data-api",se._clearMenus).on(te,ie,function(e){e.preventDefault(),e.stopPropagation(),se._jQueryInterface.call(r.default(this),"toggle")}).on(te,".dropdown form",function(e){e.stopPropagation()}),r.default.fn[z]=se._jQueryInterface,r.default.fn[z].Constructor=se,r.default.fn[z].noConflict=function(){return r.default.fn[z]=V,se._jQueryInterface};var le="bs.modal",ue=r.default.fn.modal,ce="modal-open",fe="fade",de="show",he="modal-static",pe="hidden.bs.modal",ge="show.bs.modal",me="focusin.bs.modal",ve="resize.bs.modal",ye="click.dismiss.bs.modal",_e="keydown.dismiss.bs.modal",be="mousedown.dismiss.bs.modal",we=".fixed-top, .fixed-bottom, .is-fixed, .sticky-top",Te={backdrop:!0,keyboard:!0,focus:!0,show:!0},Ee={backdrop:"(boolean|string)",keyboard:"boolean",focus:"boolean",show:"boolean"},xe=function(){function e(e,t){this._config=this._getConfig(t),this._element=e,this._dialog=e.querySelector(".modal-dialog"),this._backdrop=null,this._isShown=!1,this._isBodyOverflowing=!1,this._ignoreBackdropClick=!1,this._isTransitioning=!1,this._scrollbarWidth=0}var t=e.prototype;return t.toggle=function(e){return this._isShown?this.hide():this.show(e)},t.show=function(e){var t=this;if(!this._isShown&&!this._isTransitioning){var n=r.default.Event(ge,{relatedTarget:e});r.default(this._element).trigger(n),n.isDefaultPrevented()||(this._isShown=!0,r.default(this._element).hasClass(fe)&&(this._isTransitioning=!0),this._checkScrollbar(),this._setScrollbar(),this._adjustDialog(),this._setEscapeEvent(),this._setResizeEvent(),r.default(this._element).on(ye,'[data-dismiss="modal"]',function(e){return t.hide(e)}),r.default(this._dialog).on(be,function(){r.default(t._element).one("mouseup.dismiss.bs.modal",function(e){r.default(e.target).is(t._element)&&(t._ignoreBackdropClick=!0)})}),this._showBackdrop(function(){return t._showElement(e)}))}},t.hide=function(e){var t=this;if(e&&e.preventDefault(),this._isShown&&!this._isTransitioning){var n=r.default.Event("hide.bs.modal");if(r.default(this._element).trigger(n),this._isShown&&!n.isDefaultPrevented()){this._isShown=!1;var i=r.default(this._element).hasClass(fe);if(i&&(this._isTransitioning=!0),this._setEscapeEvent(),this._setResizeEvent(),r.default(document).off(me),r.default(this._element).removeClass(de),r.default(this._element).off(ye),r.default(this._dialog).off(be),i){var o=f.getTransitionDurationFromElement(this._element);r.default(this._element).one(f.TRANSITION_END,function(e){return t._hideModal(e)}).emulateTransitionEnd(o)}else this._hideModal()}}},t.dispose=function(){[window,this._element,this._dialog].forEach(function(e){return r.default(e).off(".bs.modal")}),r.default(document).off(me),r.default.removeData(this._element,le),this._config=null,this._element=null,this._dialog=null,this._backdrop=null,this._isShown=null,this._isBodyOverflowing=null,this._ignoreBackdropClick=null,this._isTransitioning=null,this._scrollbarWidth=null},t.handleUpdate=function(){this._adjustDialog()},t._getConfig=function(e){return e=l({},Te,e),f.typeCheckConfig("modal",e,Ee),e},t._triggerBackdropTransition=function(){var e=this,t=r.default.Event("hidePrevented.bs.modal");if(r.default(this._element).trigger(t),!t.isDefaultPrevented()){var n=this._element.scrollHeight>document.documentElement.clientHeight;n||(this._element.style.overflowY="hidden"),this._element.classList.add(he);var i=f.getTransitionDurationFromElement(this._dialog);r.default(this._element).off(f.TRANSITION_END),r.default(this._element).one(f.TRANSITION_END,function(){e._element.classList.remove(he),n||r.default(e._element).one(f.TRANSITION_END,function(){e._element.style.overflowY=""}).emulateTransitionEnd(e._element,i)}).emulateTransitionEnd(i),this._element.focus()}},t._showElement=function(e){var t=this,n=r.default(this._element).hasClass(fe),i=this._dialog?this._dialog.querySelector(".modal-body"):null;this._element.parentNode&&this._element.parentNode.nodeType===Node.ELEMENT_NODE||document.body.appendChild(this._element),this._element.style.display="block",this._element.removeAttribute("aria-hidden"),this._element.setAttribute("aria-modal",!0),this._element.setAttribute("role","dialog"),r.default(this._dialog).hasClass("modal-dialog-scrollable")&&i?i.scrollTop=0:this._element.scrollTop=0,n&&f.reflow(this._element),r.default(this._element).addClass(de),this._config.focus&&this._enforceFocus();var o=r.default.Event("shown.bs.modal",{relatedTarget:e}),a=function(){t._config.focus&&t._element.focus(),t._isTransitioning=!1,r.default(t._element).trigger(o)};if(n){var s=f.getTransitionDurationFromElement(this._dialog);r.default(this._dialog).one(f.TRANSITION_END,a).emulateTransitionEnd(s)}else a()},t._enforceFocus=function(){var e=this;r.default(document).off(me).on(me,function(t){document!==t.target&&e._element!==t.target&&0===r.default(e._element).has(t.target).length&&e._element.focus()})},t._setEscapeEvent=function(){var e=this;this._isShown?r.default(this._element).on(_e,function(t){e._config.keyboard&&27===t.which?(t.preventDefault(),e.hide()):e._config.keyboard||27!==t.which||e._triggerBackdropTransition()}):this._isShown||r.default(this._element).off(_e)},t._setResizeEvent=function(){var e=this;this._isShown?r.default(window).on(ve,function(t){return e.handleUpdate(t)}):r.default(window).off(ve)},t._hideModal=function(){var e=this;this._element.style.display="none",this._element.setAttribute("aria-hidden",!0),this._element.removeAttribute("aria-modal"),this._element.removeAttribute("role"),this._isTransitioning=!1,this._showBackdrop(function(){r.default(document.body).removeClass(ce),e._resetAdjustments(),e._resetScrollbar(),r.default(e._element).trigger(pe)})},t._removeBackdrop=function(){this._backdrop&&(r.default(this._backdrop).remove(),this._backdrop=null)},t._showBackdrop=function(e){var t=this,n=r.default(this._element).hasClass(fe)?fe:"";if(this._isShown&&this._config.backdrop){if(this._backdrop=document.createElement("div"),this._backdrop.className="modal-backdrop",n&&this._backdrop.classList.add(n),r.default(this._backdrop).appendTo(document.body),r.default(this._element).on(ye,function(e){t._ignoreBackdropClick?t._ignoreBackdropClick=!1:e.target===e.currentTarget&&("static"===t._config.backdrop?t._triggerBackdropTransition():t.hide())}),n&&f.reflow(this._backdrop),r.default(this._backdrop).addClass(de),!e)return;if(!n)return void e();var i=f.getTransitionDurationFromElement(this._backdrop);r.default(this._backdrop).one(f.TRANSITION_END,e).emulateTransitionEnd(i)}else if(!this._isShown&&this._backdrop){r.default(this._backdrop).removeClass(de);var o=function(){t._removeBackdrop(),e&&e()};if(r.default(this._element).hasClass(fe)){var a=f.getTransitionDurationFromElement(this._backdrop);r.default(this._backdrop).one(f.TRANSITION_END,o).emulateTransitionEnd(a)}else o()}else e&&e()},t._adjustDialog=function(){var e=this._element.scrollHeight>document.documentElement.clientHeight;!this._isBodyOverflowing&&e&&(this._element.style.paddingLeft=this._scrollbarWidth+"px"),this._isBodyOverflowing&&!e&&(this._element.style.paddingRight=this._scrollbarWidth+"px")},t._resetAdjustments=function(){this._element.style.paddingLeft="",this._element.style.paddingRight=""},t._checkScrollbar=function(){var e=document.body.getBoundingClientRect();this._isBodyOverflowing=Math.round(e.left+e.right)<window.innerWidth,this._scrollbarWidth=this._getScrollbarWidth()},t._setScrollbar=function(){var e=this;if(this._isBodyOverflowing){var t=[].slice.call(document.querySelectorAll(we)),n=[].slice.call(document.querySelectorAll(".sticky-top"));r.default(t).each(function(t,n){var i=n.style.paddingRight,o=r.default(n).css("padding-right");r.default(n).data("padding-right",i).css("padding-right",parseFloat(o)+e._scrollbarWidth+"px")}),r.default(n).each(function(t,n){var i=n.style.marginRight,o=r.default(n).css("margin-right");r.default(n).data("margin-right",i).css("margin-right",parseFloat(o)-e._scrollbarWidth+"px")});var i=document.body.style.paddingRight,o=r.default(document.body).css("padding-right");r.default(document.body).data("padding-right",i).css("padding-right",parseFloat(o)+this._scrollbarWidth+"px")}r.default(document.body).addClass(ce)},t._resetScrollbar=function(){var e=[].slice.call(document.querySelectorAll(we));r.default(e).each(function(e,t){var n=r.default(t).data("padding-right");r.default(t).removeData("padding-right"),t.style.paddingRight=n||""});var t=[].slice.call(document.querySelectorAll(".sticky-top"));r.default(t).each(function(e,t){var n=r.default(t).data("margin-right");void 0!==n&&r.default(t).css("margin-right",n).removeData("margin-right")});var n=r.default(document.body).data("padding-right");r.default(document.body).removeData("padding-right"),document.body.style.paddingRight=n||""},t._getScrollbarWidth=function(){var e=document.createElement("div");e.className="modal-scrollbar-measure",document.body.appendChild(e);var t=e.getBoundingClientRect().width-e.clientWidth;return document.body.removeChild(e),t},e._jQueryInterface=function(t,n){return this.each(function(){var i=r.default(this).data(le),o=l({},Te,r.default(this).data(),"object"==typeof t&&t?t:{});if(i||(i=new e(this,o),r.default(this).data(le,i)),"string"==typeof t){if(void 0===i[t])throw new TypeError('No method named "'+t+'"');i[t](n)}else o.show&&i.show(n)})},s(e,null,[{key:"VERSION",get:function(){return"4.6.2"}},{key:"Default",get:function(){return Te}}]),e}();r.default(document).on("click.bs.modal.data-api",'[data-toggle="modal"]',function(e){var t,n=this,i=f.getSelectorFromElement(this);i&&(t=document.querySelector(i));var o=r.default(t).data(le)?"toggle":l({},r.default(t).data(),r.default(this).data());"A"!==this.tagName&&"AREA"!==this.tagName||e.preventDefault();var a=r.default(t).one(ge,function(e){e.isDefaultPrevented()||a.one(pe,function(){r.default(n).is(":visible")&&n.focus()})});xe._jQueryInterface.call(r.default(t),o,this)}),r.default.fn.modal=xe._jQueryInterface,r.default.fn.modal.Constructor=xe,r.default.fn.modal.noConflict=function(){return r.default.fn.modal=ue,xe._jQueryInterface};var Ce=["background","cite","href","itemtype","longdesc","poster","src","xlink:href"],Se=/^(?:(?:https?|mailto|ftp|tel|file|sms):|[^#&/:?]*(?:[#/?]|$))/i,ke=/^data:(?:image\/(?:bmp|gif|jpeg|jpg|png|tiff|webp)|video\/(?:mpeg|mp4|ogg|webm)|audio\/(?:mp3|oga|ogg|opus));base64,[\d+/a-z]+=*$/i;function Ae(e,t,n){if(0===e.length)return e;if(n&&"function"==typeof n)return n(e);for(var i=(new window.DOMParser).parseFromString(e,"text/html"),r=Object.keys(t),o=[].slice.call(i.body.querySelectorAll("*")),a=function(e,n){var i=o[e],a=i.nodeName.toLowerCase();if(-1===r.indexOf(i.nodeName.toLowerCase()))return i.parentNode.removeChild(i),"continue";var s=[].slice.call(i.attributes),l=[].concat(t["*"]||[],t[a]||[]);s.forEach(function(e){(function(e,t){var n=e.nodeName.toLowerCase();if(-1!==t.indexOf(n))return-1===Ce.indexOf(n)||Boolean(Se.test(e.nodeValue)||ke.test(e.nodeValue));for(var i=t.filter(function(e){return e instanceof RegExp}),r=0,o=i.length;r<o;r++)if(i[r].test(n))return!0;return!1})(e,l)||i.removeAttribute(e.nodeName)})},s=0,l=o.length;s<l;s++)a(s);return i.body.innerHTML}var De="tooltip",Ne="bs.tooltip",je=r.default.fn.tooltip,Ie=new RegExp("(^|\\s)bs-tooltip\\S+","g"),Oe=["sanitize","whiteList","sanitizeFn"],Le="fade",qe="show",Pe="show",Re="out",He="hover",Fe="focus",Me={AUTO:"auto",TOP:"top",RIGHT:"right",BOTTOM:"bottom",LEFT:"left"},Be={animation:!0,template:'<div class="tooltip" role="tooltip"><div class="arrow"></div><div class="tooltip-inner"></div></div>',trigger:"hover focus",title:"",delay:0,html:!1,selector:!1,placement:"top",offset:0,container:!1,fallbackPlacement:"flip",boundary:"scrollParent",customClass:"",sanitize:!0,sanitizeFn:null,whiteList:{"*":["class","dir","id","lang","role",/^aria-[\w-]*$/i],a:["target","href","title","rel"],area:[],b:[],br:[],col:[],code:[],div:[],em:[],hr:[],h1:[],h2:[],h3:[],h4:[],h5:[],h6:[],i:[],img:["src","srcset","alt","title","width","height"],li:[],ol:[],p:[],pre:[],s:[],small:[],span:[],sub:[],sup:[],strong:[],u:[],ul:[]},popperConfig:null},We={animation:"boolean",template:"string",title:"(string|element|function)",trigger:"string",delay:"(number|object)",html:"boolean",selector:"(string|boolean)",placement:"(string|function)",offset:"(number|string|function)",container:"(string|element|boolean)",fallbackPlacement:"(string|array)",boundary:"(string|element)",customClass:"(string|function)",sanitize:"boolean",sanitizeFn:"(null|function)",whiteList:"object",popperConfig:"(null|object)"},Ue={HIDE:"hide.bs.tooltip",HIDDEN:"hidden.bs.tooltip",SHOW:"show.bs.tooltip",SHOWN:"shown.bs.tooltip",INSERTED:"inserted.bs.tooltip",CLICK:"click.bs.tooltip",FOCUSIN:"focusin.bs.tooltip",FOCUSOUT:"focusout.bs.tooltip",MOUSEENTER:"mouseenter.bs.tooltip",MOUSELEAVE:"mouseleave.bs.tooltip"},Qe=function(){function e(e,t){if(void 0===o.default)throw new TypeError("Bootstrap's tooltips require Popper (https://popper.js.org)");this._isEnabled=!0,this._timeout=0,this._hoverState="",this._activeTrigger={},this._popper=null,this.element=e,this.config=this._getConfig(t),this.tip=null,this._setListeners()}var t=e.prototype;return t.enable=function(){this._isEnabled=!0},t.disable=function(){this._isEnabled=!1},t.toggleEnabled=function(){this._isEnabled=!this._isEnabled},t.toggle=function(e){if(this._isEnabled)if(e){var t=this.constructor.DATA_KEY,n=r.default(e.currentTarget).data(t);n||(n=new this.constructor(e.currentTarget,this._getDelegateConfig()),r.default(e.currentTarget).data(t,n)),n._activeTrigger.click=!n._activeTrigger.click,n._isWithActiveTrigger()?n._enter(null,n):n._leave(null,n)}else{if(r.default(this.getTipElement()).hasClass(qe))return void this._leave(null,this);this._enter(null,this)}},t.dispose=function(){clearTimeout(this._timeout),r.default.removeData(this.element,this.constructor.DATA_KEY),r.default(this.element).off(this.constructor.EVENT_KEY),r.default(this.element).closest(".modal").off("hide.bs.modal",this._hideModalHandler),this.tip&&r.default(this.tip).remove(),this._isEnabled=null,this._timeout=null,this._hoverState=null,this._activeTrigger=null,this._popper&&this._popper.destroy(),this._popper=null,this.element=null,this.config=null,this.tip=null},t.show=function(){var e=this;if("none"===r.default(this.element).css("display"))throw new Error("Please use show on visible elements");var t=r.default.Event(this.constructor.Event.SHOW);if(this.isWithContent()&&this._isEnabled){r.default(this.element).trigger(t);var n=f.findShadowRoot(this.element),i=r.default.contains(null!==n?n:this.element.ownerDocument.documentElement,this.element);if(t.isDefaultPrevented()||!i)return;var a=this.getTipElement(),s=f.getUID(this.constructor.NAME);a.setAttribute("id",s),this.element.setAttribute("aria-describedby",s),this.setContent(),this.config.animation&&r.default(a).addClass(Le);var l="function"==typeof this.config.placement?this.config.placement.call(this,a,this.element):this.config.placement,u=this._getAttachment(l);this.addAttachmentClass(u);var c=this._getContainer();r.default(a).data(this.constructor.DATA_KEY,this),r.default.contains(this.element.ownerDocument.documentElement,this.tip)||r.default(a).appendTo(c),r.default(this.element).trigger(this.constructor.Event.INSERTED),this._popper=new o.default(this.element,a,this._getPopperConfig(u)),r.default(a).addClass(qe),r.default(a).addClass(this.config.customClass),"ontouchstart"in document.documentElement&&r.default(document.body).children().on("mouseover",null,r.default.noop);var d=function(){e.config.animation&&e._fixTransition();var t=e._hoverState;e._hoverState=null,r.default(e.element).trigger(e.constructor.Event.SHOWN),t===Re&&e._leave(null,e)};if(r.default(this.tip).hasClass(Le)){var h=f.getTransitionDurationFromElement(this.tip);r.default(this.tip).one(f.TRANSITION_END,d).emulateTransitionEnd(h)}else d()}},t.hide=function(e){var t=this,n=this.getTipElement(),i=r.default.Event(this.constructor.Event.HIDE),o=function(){t._hoverState!==Pe&&n.parentNode&&n.parentNode.removeChild(n),t._cleanTipClass(),t.element.removeAttribute("aria-describedby"),r.default(t.element).trigger(t.constructor.Event.HIDDEN),null!==t._popper&&t._popper.destroy(),e&&e()};if(r.default(this.element).trigger(i),!i.isDefaultPrevented()){if(r.default(n).removeClass(qe),"ontouchstart"in document.documentElement&&r.default(document.body).children().off("mouseover",null,r.default.noop),this._activeTrigger.click=!1,this._activeTrigger.focus=!1,this._activeTrigger.hover=!1,r.default(this.tip).hasClass(Le)){var a=f.getTransitionDurationFromElement(n);r.default(n).one(f.TRANSITION_END,o).emulateTransitionEnd(a)}else o();this._hoverState=""}},t.update=function(){null!==this._popper&&this._popper.scheduleUpdate()},t.isWithContent=function(){return Boolean(this.getTitle())},t.addAttachmentClass=function(e){r.default(this.getTipElement()).addClass("bs-tooltip-"+e)},t.getTipElement=function(){return this.tip=this.tip||r.default(this.config.template)[0],this.tip},t.setContent=function(){var e=this.getTipElement();this.setElementContent(r.default(e.querySelectorAll(".tooltip-inner")),this.getTitle()),r.default(e).removeClass("fade show")},t.setElementContent=function(e,t){"object"!=typeof t||!t.nodeType&&!t.jquery?this.config.html?(this.config.sanitize&&(t=Ae(t,this.config.whiteList,this.config.sanitizeFn)),e.html(t)):e.text(t):this.config.html?r.default(t).parent().is(e)||e.empty().append(t):e.text(r.default(t).text())},t.getTitle=function(){var e=this.element.getAttribute("data-original-title");return e||(e="function"==typeof this.config.title?this.config.title.call(this.element):this.config.title),e},t._getPopperConfig=function(e){var t=this;return l({},{placement:e,modifiers:{offset:this._getOffset(),flip:{behavior:this.config.fallbackPlacement},arrow:{element:".arrow"},preventOverflow:{boundariesElement:this.config.boundary}},onCreate:function(e){e.originalPlacement!==e.placement&&t._handlePopperPlacementChange(e)},onUpdate:function(e){return t._handlePopperPlacementChange(e)}},this.config.popperConfig)},t._getOffset=function(){var e=this,t={};return"function"==typeof this.config.offset?t.fn=function(t){return t.offsets=l({},t.offsets,e.config.offset(t.offsets,e.element)),t}:t.offset=this.config.offset,t},t._getContainer=function(){return!1===this.config.container?document.body:f.isElement(this.config.container)?r.default(this.config.container):r.default(document).find(this.config.container)},t._getAttachment=function(e){return Me[e.toUpperCase()]},t._setListeners=function(){var e=this;this.config.trigger.split(" ").forEach(function(t){if("click"===t)r.default(e.element).on(e.constructor.Event.CLICK,e.config.selector,function(t){return e.toggle(t)});else if("manual"!==t){var n=t===He?e.constructor.Event.MOUSEENTER:e.constructor.Event.FOCUSIN,i=t===He?e.constructor.Event.MOUSELEAVE:e.constructor.Event.FOCUSOUT;r.default(e.element).on(n,e.config.selector,function(t){return e._enter(t)}).on(i,e.config.selector,function(t){return e._leave(t)})}}),this._hideModalHandler=function(){e.element&&e.hide()},r.default(this.element).closest(".modal").on("hide.bs.modal",this._hideModalHandler),this.config.selector?this.config=l({},this.config,{trigger:"manual",selector:""}):this._fixTitle()},t._fixTitle=function(){var e=typeof this.element.getAttribute("data-original-title");(this.element.getAttribute("title")||"string"!==e)&&(this.element.setAttribute("data-original-title",this.element.getAttribute("title")||""),this.element.setAttribute("title",""))},t._enter=function(e,t){var n=this.constructor.DATA_KEY;(t=t||r.default(e.currentTarget).data(n))||(t=new this.constructor(e.currentTarget,this._getDelegateConfig()),r.default(e.currentTarget).data(n,t)),e&&(t._activeTrigger["focusin"===e.type?Fe:He]=!0),r.default(t.getTipElement()).hasClass(qe)||t._hoverState===Pe?t._hoverState=Pe:(clearTimeout(t._timeout),t._hoverState=Pe,t.config.delay&&t.config.delay.show?t._timeout=setTimeout(function(){t._hoverState===Pe&&t.show()},t.config.delay.show):t.show())},t._leave=function(e,t){var n=this.constructor.DATA_KEY;(t=t||r.default(e.currentTarget).data(n))||(t=new this.constructor(e.currentTarget,this._getDelegateConfig()),r.default(e.currentTarget).data(n,t)),e&&(t._activeTrigger["focusout"===e.type?Fe:He]=!1),t._isWithActiveTrigger()||(clearTimeout(t._timeout),t._hoverState=Re,t.config.delay&&t.config.delay.hide?t._timeout=setTimeout(function(){t._hoverState===Re&&t.hide()},t.config.delay.hide):t.hide())},t._isWithActiveTrigger=function(){for(var e in this._activeTrigger)if(this._activeTrigger[e])return!0;return!1},t._getConfig=function(e){var t=r.default(this.element).data();return Object.keys(t).forEach(function(e){-1!==Oe.indexOf(e)&&delete t[e]}),"number"==typeof(e=l({},this.constructor.Default,t,"object"==typeof e&&e?e:{})).delay&&(e.delay={show:e.delay,hide:e.delay}),"number"==typeof e.title&&(e.title=e.title.toString()),"number"==typeof e.content&&(e.content=e.content.toString()),f.typeCheckConfig(De,e,this.constructor.DefaultType),e.sanitize&&(e.template=Ae(e.template,e.whiteList,e.sanitizeFn)),e},t._getDelegateConfig=function(){var e={};if(this.config)for(var t in this.config)this.constructor.Default[t]!==this.config[t]&&(e[t]=this.config[t]);return e},t._cleanTipClass=function(){var e=r.default(this.getTipElement()),t=e.attr("class").match(Ie);null!==t&&t.length&&e.removeClass(t.join(""))},t._handlePopperPlacementChange=function(e){this.tip=e.instance.popper,this._cleanTipClass(),this.addAttachmentClass(this._getAttachment(e.placement))},t._fixTransition=function(){var e=this.getTipElement(),t=this.config.animation;null===e.getAttribute("x-placement")&&(r.default(e).removeClass(Le),this.config.animation=!1,this.hide(),this.show(),this.config.animation=t)},e._jQueryInterface=function(t){return this.each(function(){var n=r.default(this),i=n.data(Ne),o="object"==typeof t&&t;if((i||!/dispose|hide/.test(t))&&(i||(i=new e(this,o),n.data(Ne,i)),"string"==typeof t)){if(void 0===i[t])throw new TypeError('No method named "'+t+'"');i[t]()}})},s(e,null,[{key:"VERSION",get:function(){return"4.6.2"}},{key:"Default",get:function(){return Be}},{key:"NAME",get:function(){return De}},{key:"DATA_KEY",get:function(){return Ne}},{key:"Event",get:function(){return Ue}},{key:"EVENT_KEY",get:function(){return".bs.tooltip"}},{key:"DefaultType",get:function(){return We}}]),e}();r.default.fn.tooltip=Qe._jQueryInterface,r.default.fn.tooltip.Constructor=Qe,r.default.fn.tooltip.noConflict=function(){return r.default.fn.tooltip=je,Qe._jQueryInterface};var $e="bs.popover",ze=r.default.fn.popover,Xe=new RegExp("(^|\\s)bs-popover\\S+","g"),Ve=l({},Qe.Default,{placement:"right",trigger:"click",content:"",template:'<div class="popover" role="tooltip"><div class="arrow"></div><h3 class="popover-header"></h3><div class="popover-body"></div></div>'}),Ye=l({},Qe.DefaultType,{content:"(string|element|function)"}),Ke={HIDE:"hide.bs.popover",HIDDEN:"hidden.bs.popover",SHOW:"show.bs.popover",SHOWN:"shown.bs.popover",INSERTED:"inserted.bs.popover",CLICK:"click.bs.popover",FOCUSIN:"focusin.bs.popover",FOCUSOUT:"focusout.bs.popover",MOUSEENTER:"mouseenter.bs.popover",MOUSELEAVE:"mouseleave.bs.popover"},Ge=function(e){var t,n;function i(){return e.apply(this,arguments)||this}n=e,(t=i).prototype=Object.create(n.prototype),t.prototype.constructor=t,u(t,n);var o=i.prototype;return o.isWithContent=function(){return this.getTitle()||this._getContent()},o.addAttachmentClass=function(e){r.default(this.getTipElement()).addClass("bs-popover-"+e)},o.getTipElement=function(){return this.tip=this.tip||r.default(this.config.template)[0],this.tip},o.setContent=function(){var e=r.default(this.getTipElement());this.setElementContent(e.find(".popover-header"),this.getTitle());var t=this._getContent();"function"==typeof t&&(t=t.call(this.element)),this.setElementContent(e.find(".popover-body"),t),e.removeClass("fade show")},o._getContent=function(){return this.element.getAttribute("data-content")||this.config.content},o._cleanTipClass=function(){var e=r.default(this.getTipElement()),t=e.attr("class").match(Xe);null!==t&&t.length>0&&e.removeClass(t.join(""))},i._jQueryInterface=function(e){return this.each(function(){var t=r.default(this).data($e),n="object"==typeof e?e:null;if((t||!/dispose|hide/.test(e))&&(t||(t=new i(this,n),r.default(this).data($e,t)),"string"==typeof e)){if(void 0===t[e])throw new TypeError('No method named "'+e+'"');t[e]()}})},s(i,null,[{key:"VERSION",get:function(){return"4.6.2"}},{key:"Default",get:function(){return Ve}},{key:"NAME",get:function(){return"popover"}},{key:"DATA_KEY",get:function(){return $e}},{key:"Event",get:function(){return Ke}},{key:"EVENT_KEY",get:function(){return".bs.popover"}},{key:"DefaultType",get:function(){return Ye}}]),i}(Qe);r.default.fn.popover=Ge._jQueryInterface,r.default.fn.popover.Constructor=Ge,r.default.fn.popover.noConflict=function(){return r.default.fn.popover=ze,Ge._jQueryInterface};var Je="scrollspy",Ze="bs.scrollspy",et=r.default.fn[Je],tt="active",nt="position",it=".nav, .list-group",rt={offset:10,method:"auto",target:""},ot={offset:"number",method:"string",target:"(string|element)"},at=function(){function e(e,t){var n=this;this._element=e,this._scrollElement="BODY"===e.tagName?window:e,this._config=this._getConfig(t),this._selector=this._config.target+" .nav-link,"+this._config.target+" .list-group-item,"+this._config.target+" .dropdown-item",this._offsets=[],this._targets=[],this._activeTarget=null,this._scrollHeight=0,r.default(this._scrollElement).on("scroll.bs.scrollspy",function(e){return n._process(e)}),this.refresh(),this._process()}var t=e.prototype;return t.refresh=function(){var e=this,t="auto"===this._config.method?this._scrollElement===this._scrollElement.window?"offset":nt:this._config.method,n=t===nt?this._getScrollTop():0;this._offsets=[],this._targets=[],this._scrollHeight=this._getScrollHeight(),[].slice.call(document.querySelectorAll(this._selector)).map(function(e){var i,o=f.getSelectorFromElement(e);if(o&&(i=document.querySelector(o)),i){var a=i.getBoundingClientRect();if(a.width||a.height)return[r.default(i)[t]().top+n,o]}return null}).filter(Boolean).sort(function(e,t){return e[0]-t[0]}).forEach(function(t){e._offsets.push(t[0]),e._targets.push(t[1])})},t.dispose=function(){r.default.removeData(this._element,Ze),r.default(this._scrollElement).off(".bs.scrollspy"),this._element=null,this._scrollElement=null,this._config=null,this._selector=null,this._offsets=null,this._targets=null,this._activeTarget=null,this._scrollHeight=null},t._getConfig=function(e){if("string"!=typeof(e=l({},rt,"object"==typeof e&&e?e:{})).target&&f.isElement(e.target)){var t=r.default(e.target).attr("id");t||(t=f.getUID(Je),r.default(e.target).attr("id",t)),e.target="#"+t}return f.typeCheckConfig(Je,e,ot),e},t._getScrollTop=function(){return this._scrollElement===window?this._scrollElement.pageYOffset:this._scrollElement.scrollTop},t._getScrollHeight=function(){return this._scrollElement.scrollHeight||Math.max(document.body.scrollHeight,document.documentElement.scrollHeight)},t._getOffsetHeight=function(){return this._scrollElement===window?window.innerHeight:this._scrollElement.getBoundingClientRect().height},t._process=function(){var e=this._getScrollTop()+this._config.offset,t=this._getScrollHeight(),n=this._config.offset+t-this._getOffsetHeight();if(this._scrollHeight!==t&&this.refresh(),e>=n){var i=this._targets[this._targets.length-1];this._activeTarget!==i&&this._activate(i)}else{if(this._activeTarget&&e<this._offsets[0]&&this._offsets[0]>0)return this._activeTarget=null,void this._clear();for(var r=this._offsets.length;r--;)this._activeTarget!==this._targets[r]&&e>=this._offsets[r]&&(void 0===this._offsets[r+1]||e<this._offsets[r+1])&&this._activate(this._targets[r])}},t._activate=function(e){this._activeTarget=e,this._clear();var t=this._selector.split(",").map(function(t){return t+'[data-target="'+e+'"],'+t+'[href="'+e+'"]'}),n=r.default([].slice.call(document.querySelectorAll(t.join(","))));n.hasClass("dropdown-item")?(n.closest(".dropdown").find(".dropdown-toggle").addClass(tt),n.addClass(tt)):(n.addClass(tt),n.parents(it).prev(".nav-link, .list-group-item").addClass(tt),n.parents(it).prev(".nav-item").children(".nav-link").addClass(tt)),r.default(this._scrollElement).trigger("activate.bs.scrollspy",{relatedTarget:e})},t._clear=function(){[].slice.call(document.querySelectorAll(this._selector)).filter(function(e){return e.classList.contains(tt)}).forEach(function(e){return e.classList.remove(tt)})},e._jQueryInterface=function(t){return this.each(function(){var n=r.default(this).data(Ze);if(n||(n=new e(this,"object"==typeof t&&t),r.default(this).data(Ze,n)),"string"==typeof t){if(void 0===n[t])throw new TypeError('No method named "'+t+'"');n[t]()}})},s(e,null,[{key:"VERSION",get:function(){return"4.6.2"}},{key:"Default",get:function(){return rt}}]),e}();r.default(window).on("load.bs.scrollspy.data-api",function(){for(var e=[].slice.call(document.querySelectorAll('[data-spy="scroll"]')),t=e.length;t--;){var n=r.default(e[t]);at._jQueryInterface.call(n,n.data())}}),r.default.fn[Je]=at._jQueryInterface,r.default.fn[Je].Constructor=at,r.default.fn[Je].noConflict=function(){return r.default.fn[Je]=et,at._jQueryInterface};var st="bs.tab",lt=r.default.fn.tab,ut="active",ct="fade",ft="show",dt=".active",ht="> li > .active",pt=function(){function e(e){this._element=e}var t=e.prototype;return t.show=function(){var e=this;if(!(this._element.parentNode&&this._element.parentNode.nodeType===Node.ELEMENT_NODE&&r.default(this._element).hasClass(ut)||r.default(this._element).hasClass("disabled")||this._element.hasAttribute("disabled"))){var t,n,i=r.default(this._element).closest(".nav, .list-group")[0],o=f.getSelectorFromElement(this._element);if(i){var a="UL"===i.nodeName||"OL"===i.nodeName?ht:dt;n=(n=r.default.makeArray(r.default(i).find(a)))[n.length-1]}var s=r.default.Event("hide.bs.tab",{relatedTarget:this._element}),l=r.default.Event("show.bs.tab",{relatedTarget:n});if(n&&r.default(n).trigger(s),r.default(this._element).trigger(l),!l.isDefaultPrevented()&&!s.isDefaultPrevented()){o&&(t=document.querySelector(o)),this._activate(this._element,i);var u=function(){var t=r.default.Event("hidden.bs.tab",{relatedTarget:e._element}),i=r.default.Event("shown.bs.tab",{relatedTarget:n});r.default(n).trigger(t),r.default(e._element).trigger(i)};t?this._activate(t,t.parentNode,u):u()}}},t.dispose=function(){r.default.removeData(this._element,st),this._element=null},t._activate=function(e,t,n){var i=this,o=(!t||"UL"!==t.nodeName&&"OL"!==t.nodeName?r.default(t).children(dt):r.default(t).find(ht))[0],a=n&&o&&r.default(o).hasClass(ct),s=function(){return i._transitionComplete(e,o,n)};if(o&&a){var l=f.getTransitionDurationFromElement(o);r.default(o).removeClass(ft).one(f.TRANSITION_END,s).emulateTransitionEnd(l)}else s()},t._transitionComplete=function(e,t,n){if(t){r.default(t).removeClass(ut);var i=r.default(t.parentNode).find("> .dropdown-menu .active")[0];i&&r.default(i).removeClass(ut),"tab"===t.getAttribute("role")&&t.setAttribute("aria-selected",!1)}r.default(e).addClass(ut),"tab"===e.getAttribute("role")&&e.setAttribute("aria-selected",!0),f.reflow(e),e.classList.contains(ct)&&e.classList.add(ft);var o=e.parentNode;if(o&&"LI"===o.nodeName&&(o=o.parentNode),o&&r.default(o).hasClass("dropdown-menu")){var a=r.default(e).closest(".dropdown")[0];if(a){var s=[].slice.call(a.querySelectorAll(".dropdown-toggle"));r.default(s).addClass(ut)}e.setAttribute("aria-expanded",!0)}n&&n()},e._jQueryInterface=function(t){return this.each(function(){var n=r.default(this),i=n.data(st);if(i||(i=new e(this),n.data(st,i)),"string"==typeof t){if(void 0===i[t])throw new TypeError('No method named "'+t+'"');i[t]()}})},s(e,null,[{key:"VERSION",get:function(){return"4.6.2"}}]),e}();r.default(document).on("click.bs.tab.data-api",'[data-toggle="tab"], [data-toggle="pill"], [data-toggle="list"]',function(e){e.preventDefault(),pt._jQueryInterface.call(r.default(this),"show")}),r.default.fn.tab=pt._jQueryInterface,r.default.fn.tab.Constructor=pt,r.default.fn.tab.noConflict=function(){return r.default.fn.tab=lt,pt._jQueryInterface};var gt="bs.toast",mt=r.default.fn.toast,vt="hide",yt="show",_t="showing",bt="click.dismiss.bs.toast",wt={animation:!0,autohide:!0,delay:500},Tt={animation:"boolean",autohide:"boolean",delay:"number"},Et=function(){function e(e,t){this._element=e,this._config=this._getConfig(t),this._timeout=null,this._setListeners()}var t=e.prototype;return t.show=function(){var e=this,t=r.default.Event("show.bs.toast");if(r.default(this._element).trigger(t),!t.isDefaultPrevented()){this._clearTimeout(),this._config.animation&&this._element.classList.add("fade");var n=function(){e._element.classList.remove(_t),e._element.classList.add(yt),r.default(e._element).trigger("shown.bs.toast"),e._config.autohide&&(e._timeout=setTimeout(function(){e.hide()},e._config.delay))};if(this._element.classList.remove(vt),f.reflow(this._element),this._element.classList.add(_t),this._config.animation){var i=f.getTransitionDurationFromElement(this._element);r.default(this._element).one(f.TRANSITION_END,n).emulateTransitionEnd(i)}else n()}},t.hide=function(){if(this._element.classList.contains(yt)){var e=r.default.Event("hide.bs.toast");r.default(this._element).trigger(e),e.isDefaultPrevented()||this._close()}},t.dispose=function(){this._clearTimeout(),this._element.classList.contains(yt)&&this._element.classList.remove(yt),r.default(this._element).off(bt),r.default.removeData(this._element,gt),this._element=null,this._config=null},t._getConfig=function(e){return e=l({},wt,r.default(this._element).data(),"object"==typeof e&&e?e:{}),f.typeCheckConfig("toast",e,this.constructor.DefaultType),e},t._setListeners=function(){var e=this;r.default(this._element).on(bt,'[data-dismiss="toast"]',function(){return e.hide()})},t._close=function(){var e=this,t=function(){e._element.classList.add(vt),r.default(e._element).trigger("hidden.bs.toast")};if(this._element.classList.remove(yt),this._config.animation){var n=f.getTransitionDurationFromElement(this._element);r.default(this._element).one(f.TRANSITION_END,t).emulateTransitionEnd(n)}else t()},t._clearTimeout=function(){clearTimeout(this._timeout),this._timeout=null},e._jQueryInterface=function(t){return this.each(function(){var n=r.default(this),i=n.data(gt);if(i||(i=new e(this,"object"==typeof t&&t),n.data(gt,i)),"string"==typeof t){if(void 0===i[t])throw new TypeError('No method named "'+t+'"');i[t](this)}})},s(e,null,[{key:"VERSION",get:function(){return"4.6.2"}},{key:"DefaultType",get:function(){return Tt}},{key:"Default",get:function(){return wt}}]),e}();r.default.fn.toast=Et._jQueryInterface,r.default.fn.toast.Constructor=Et,r.default.fn.toast.noConflict=function(){return r.default.fn.toast=mt,Et._jQueryInterface},e.Alert=p,e.Button=w,e.Carousel=O,e.Collapse=$,e.Dropdown=se,e.Modal=xe,e.Popover=Ge,e.Scrollspy=at,e.Tab=pt,e.Toast=Et,e.Tooltip=Qe,e.Util=f,Object.defineProperty(e,"__esModule",{value:!0})});