import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import {
  ApiResponseModel,
  ApiVisa, Core2Config, Pagination, PagingResponseModel,
  InvSerialNumberService, BatchHdrService, BinHdrService,
  SerialNumberContainerModel, BatchHdrModel, BinHdrModel,
  FinancialItemService, FinancialItemContainerModel, BasicApiResponseModel,
  PricingSchemeLinkService, PricingSchemeLinkContainerModel,
  PricingSchemeService, PricingSchemeContainerModel,
  PricingSchemeListService, PricingSchemeListContainerModel,
  LabelHdrService, LabelHeaderContainerModel,
  LabelLinkService, LabelLinkContainerModel,
  LabelListService, LabelListContainerModel
} from 'blg-akaun-ts-lib';
import { Observable, of } from 'rxjs';
import { ViewColumnFacade } from '../facades/view-column.facade';
import { SelectUomModel, StockAvailabilityInputModel, StockAvailabilityModel } from '../models/stock-availability-model';
import { UtilitiesModule } from 'projects/shared-utilities/utilities/utilities.module';
import { StockReportInputModel } from '../models/../models/stock-report-model'

@Injectable({providedIn: 'root'})
export class ApiService {
  readonly url = Core2Config.DOMAIN_URL + Core2Config.TENANT_DOMAIN_URL_PREFIX + Core2Config.ERP_PREFIX + Core2Config.INV_PREFIX + 'stock-availability';

  protected apiUrl: string;
  protected api_domain_url: string;
  protected endpoint_path: string;

  protected httpClient: HttpClient;

  constructor(http: HttpClient,
      protected viewColFacade: ViewColumnFacade,
      protected invSerialNumberService: InvSerialNumberService,
      protected batchService: BatchHdrService,
      protected binService: BinHdrService,
      protected fiItemService: FinancialItemService,
      protected pricingSchemeLinkService: PricingSchemeLinkService,
      protected pricingSchemeService : PricingSchemeService,
      protected pricingSchemeListService: PricingSchemeListService,
      protected labelLinkService: LabelLinkService,
      protected labelHdrService: LabelHdrService,
      protected labelListService: LabelListService
  ) {
    this.apiUrl = this.url;
    this.endpoint_path = Core2Config.TENANT_DOMAIN_URL_PREFIX + Core2Config.ERP_PREFIX + Core2Config.INV_PREFIX + 'stock-availability';
    this.httpClient = http;
  }

  public getApiUrl(apiVisa: ApiVisa) {
    let url = this.apiUrl;
    if (this.endpoint_path && apiVisa.api_domain_url) {
      url = apiVisa.api_domain_url + this.endpoint_path;
    }
    return url;
  }

  public getHttpHeader(apiVisa: ApiVisa) {
    apiVisa.applet_code = apiVisa.applet_code ? apiVisa.applet_code : 'none';
    apiVisa.tenantCode = apiVisa.tenantCode ? apiVisa.tenantCode : '';

    const httpOptions = {
      headers: new HttpHeaders({
        authorization: apiVisa.jwt_secret,
        tenantCode: apiVisa.tenantCode, /// this will be removed in the future
        appId: apiVisa.applet_code, /// this will be removed in the future
      })
    };

    return httpOptions;
  }


  public getStockAvailability(inputModel: StockAvailabilityInputModel, apiVisa: ApiVisa): Observable<ApiResponseModel<any>> {
    let url = this.getApiUrl(apiVisa);
    let formData = {
      //"inventory_item_guids": ["ad733ce3-25f6-4c53-ad1c-84f4fdc639ce"],
      //"location_guids" : ["a4359b44-158c-4357-8cf2-a35da184e68c"]
    };
    if (inputModel) formData = inputModel;

    return this.httpClient.post<ApiResponseModel<any>>(
      url,
      formData,
      this.getHttpHeader(apiVisa)
    );
  }

  public getSerialNumber(entity: StockAvailabilityModel, apiVisa: ApiVisa): Observable<PagingResponseModel<SerialNumberContainerModel>> {
    const limit = 5000;
    const criteria = [
      //{ columnName: 'calcTotalRecords', operator: '=', value: 'true' },
      { columnName: 'hdr_item_guid', operator: '=', value: entity.inv_item_guid },
      { columnName: 'location_guid', operator: '=', value: entity.location_guid },
    ];

    const sortCriteria = [
      { columnName: 'orderBy', value: 'sn_id' },
      { columnName: 'order', value: 'ASC' }
    ];
    const pagination = new Pagination(0, limit, criteria, sortCriteria);
    return this.invSerialNumberService.getByCriteria(pagination, apiVisa);
  }

  public getBatchNumbers(entity: StockAvailabilityModel, apiVisa: ApiVisa): Observable<PagingResponseModel<BatchHdrModel>> {
    const limit = 5000;
    const criteria = [
      //{ columnName: 'calcTotalRecords', operator: '=', value: 'true' },
      { columnName: 'inv_item_guid', operator: '=', value: entity.inv_item_guid },
      { columnName: 'guid_location', operator: '=', value: entity.location_guid },
    ];

    const sortCriteria = [
      { columnName: 'orderBy', value: 'batch_no' },
      { columnName: 'order', value: 'ASC' }
    ];
    const pagination = new Pagination(0, limit, criteria, sortCriteria);
    return this.batchService.getByCriteria(pagination, apiVisa);
  }

  public getBinNumbers(entity: StockAvailabilityModel, apiVisa: ApiVisa): Observable<PagingResponseModel<BinHdrModel>> {
    const limit = 5000;
    const criteria = [
      //{ columnName: 'calcTotalRecords', operator: '=', value: 'true' },
      { columnName: 'item_guid', operator: '=', value: entity.inv_item_guid },
      { columnName: 'guid_location', operator: '=', value: entity.location_guid },
    ];

    const sortCriteria = [
      { columnName: 'orderBy', value: 'code' },
      { columnName: 'order', value: 'ASC' }
    ];
    const pagination = new Pagination(0, limit, criteria, sortCriteria);
    return this.binService.getByCriteria(pagination, apiVisa);
  }

  public getFiItem(guid: string, apiVisa: ApiVisa): Observable<BasicApiResponseModel<FinancialItemContainerModel>> {
    return this.fiItemService.getByGuid(guid, apiVisa);
  }

  public getPricingSchemeLink(entity: SelectUomModel, pricingSchemeGuid: string, apiVisa: ApiVisa): Observable<PagingResponseModel<PricingSchemeLinkContainerModel>> {
    const limit = 100;
    const criteria = [
      { columnName: 'guid_pricing_scheme_hdr', operator: '=', value: pricingSchemeGuid },
      { columnName: 'item_hdr_guid', operator: '=', value: entity.item_hdr_guid },
      {
        columnName: 'item_line_guid',
        operator: '=',
        value: entity.item_line_guid? entity.item_line_guid: '',
      },
    ];

    const sortCriteria = [];
    const pagination = new Pagination(0, limit, criteria, sortCriteria);
    return this.pricingSchemeLinkService.getByCriteria(pagination, apiVisa);
  }

  public getPricingScheme(guid: string, apiVisa: ApiVisa): Observable<PagingResponseModel<PricingSchemeContainerModel>> {
    const limit = 100;
    const criteria = [
      {
        columnName: "label_list_guid",
        operator: "=",
        value: guid,
      },
    ];

    const sortCriteria = [
      { columnName: 'orderBy', value: 'code ' },
      { columnName: 'order', value: 'ASC' },
    ];
    const pagination = new Pagination(0, limit, criteria, sortCriteria);
    return this.pricingSchemeService.getByCriteria(pagination, apiVisa);
  }

  public getPricingSchemeList(apiVisa: ApiVisa): Observable<PagingResponseModel<PricingSchemeListContainerModel>> {
    const limit = 100;
    const criteria = [
      {
        columnName: "code",
        operator: "=",
        value: "PRICING_SCHEME_ITEM",
      },
    ];

    const sortCriteria = [];

    const pagination = new Pagination(0, limit, criteria, sortCriteria);
    return this.pricingSchemeListService.getByCriteria(pagination, apiVisa);
  }

  public getImage(guid: string, apiVisa: ApiVisa): Observable<Blob> {
    const endpoint = 'core2/tnt/dm/erp/fi/fi-items/file';
    let url = 'https://api.akaun.com/core2/tnt/dm/erp/fi/fi-items/file/';
    if (apiVisa.api_domain_url) {
      url = apiVisa.api_domain_url + endpoint;
    }
    url += "/" + guid;
    const headers = new HttpHeaders({
      authorization: apiVisa.jwt_secret,
      tenantCode: apiVisa.tenantCode,
      appId: apiVisa.applet_code,
    });
    return this.httpClient.get(url, { headers, responseType: 'blob' });
  }

  public getImageFromHdr(guid: string, apiVisa: ApiVisa): Observable<Blob> {
    const endpoint = 'core2/tnt/dm/erp/fi/fi-items/image-file';
    let url = 'https://api.akaun.com/core2/tnt/dm/erp/fi/fi-items/image-file/';
    if (apiVisa.api_domain_url) {
      url = apiVisa.api_domain_url + endpoint;
    }
    url += "/" + guid;
    const headers = new HttpHeaders({
      authorization: apiVisa.jwt_secret,
      tenantCode: apiVisa.tenantCode,
      appId: apiVisa.applet_code,
    });
    return this.httpClient.get(url, { headers, responseType: 'blob' });
  }

  public getLabelLink(itemGuid: string, apiVisa: ApiVisa): Observable<PagingResponseModel<LabelLinkContainerModel>> {
    const limit = 100;
    const criteria = [
      { columnName: 'guid_hdr', operator: '=', value: itemGuid },
    ];

    const sortCriteria = [];
    const pagination = new Pagination(0, limit, criteria, sortCriteria);
    return this.labelLinkService.getByCriteria(pagination, apiVisa);
  }

  public getLabel(guid: string, apiVisa: ApiVisa): Observable<BasicApiResponseModel<LabelHeaderContainerModel>> {
    return this.labelHdrService.getByGuid(guid, apiVisa);
  }

  public getLabelList(guid: string, apiVisa: ApiVisa): Observable<BasicApiResponseModel<LabelListContainerModel>> {
    return this.labelListService.getByGuid(guid, apiVisa);
  }

  public getStockAvailabilityDetails(inputModel: StockAvailabilityInputModel, apiVisa: ApiVisa): Observable<ApiResponseModel<any>> {
    let url = this.getApiUrl(apiVisa);
    let formData = {};
    if (inputModel) formData = inputModel;

    url += "/details/backoffice-ep"
    return this.httpClient.post<ApiResponseModel<any>>(
      url,
      formData,
      this.getHttpHeader(apiVisa)
    );
  }

  public getStockAgingReport(inputModel: StockReportInputModel, apiVisa: ApiVisa): Observable<ApiResponseModel<any>> {
    const endpoint = 'core2/tnt/dm/erp/reports/stock/stock-aging-report';
    let formData = {
      "date_from": UtilitiesModule.getTodayNoTime().toISOString(),
      "date_to": UtilitiesModule.getTodayNoTime().toISOString(),
    };

    if (inputModel) formData = inputModel;

    let url = 'https://api.akaun.com/core2/tnt/dm/erp/reports/stock/stock-aging-report';
    if (apiVisa.api_domain_url) {
      url = apiVisa.api_domain_url + endpoint;
    }
    return this.httpClient.post<ApiResponseModel<any>>(
      url,
      formData,
      this.getHttpHeader(apiVisa)
    );
  }

  public getStockMovementReport(inputModel: any, apiVisa: ApiVisa): Observable<ApiResponseModel<any>> {
    const endpoint = 'core2/tnt/dm/erp/reports/stock/stock-movement-report';
    let formData = {
      "date_from": UtilitiesModule.getTodayNoTime().toISOString(),
      "date_to": UtilitiesModule.getTodayNoTime().toISOString(),
    };

    if (inputModel) formData = inputModel;

    let url = 'https://api.akaun.com/core2/tnt/dm/erp/reports/stock/stock-movement-report';
    if (apiVisa.api_domain_url) {
      url = apiVisa.api_domain_url + endpoint;
    }
    return this.httpClient.post<ApiResponseModel<any>>(
      url,
      formData,
      this.getHttpHeader(apiVisa)
    );
  }

  public getStockAvailabilityOpenQueue(inputModel: any, apiVisa: ApiVisa): Observable<ApiResponseModel<any>> {
    let url = this.getApiUrl(apiVisa);
    let formData = {};
    if (inputModel) formData = inputModel;

    url += "/open-queue/backoffice-ep"
    return this.httpClient.post<ApiResponseModel<any>>(
      url,
      formData,
      this.getHttpHeader(apiVisa)
    );
  }

  public getSerialNumberBalance(inputModel: StockReportInputModel, apiVisa: ApiVisa): Observable<ApiResponseModel<any>> {
    const endpoint = 'core2/tnt/dm/erp/serial-number-balance/backoffice-ep';
    let formData = {
      "date_from": UtilitiesModule.getTodayNoTime().toISOString(),
      "date_to": UtilitiesModule.getTodayNoTime().toISOString(),
    };

    if (inputModel) formData = inputModel;

    let url = 'https://api.akaun.com/core2/tnt/dm/erp/serial-number-balance/backoffice-ep';
    if (apiVisa.api_domain_url) {
      url = apiVisa.api_domain_url + endpoint;
    }
    return this.httpClient.post<ApiResponseModel<any>>(
      url,
      formData,
      this.getHttpHeader(apiVisa)
    );
  }

  public getSalesReportByDocument(inputModel: any, apiVisa: ApiVisa): Observable<ApiResponseModel<any>> {
    const endpoint = 'core2/tnt/dm/erp/reports/sales/sales-report-by-document';
    let formData = {
      "date_from": UtilitiesModule.getTodayNoTime().toISOString(),
      "date_to": UtilitiesModule.getTodayNoTime().toISOString(),
    };

    if (inputModel) formData = inputModel;

    let url = 'https://api.akaun.com/core2/tnt/dm/erp/reports/sales/sales-report-by-document';
    if (apiVisa.api_domain_url) {
      url = apiVisa.api_domain_url + endpoint;
    }
    return this.httpClient.post<ApiResponseModel<any>>(
      url,
      formData,
      this.getHttpHeader(apiVisa)
    );
  }
}
