<mat-card-title class="column-title">
  <div fxLayout="row wrap" fxLayoutAlign="space-between end">
    <div>
      <button #navBtn mat-button class="blg-button-icon" matTooltip="Back" type="button"
        [disabled]="deactivateReturn$ | async" (click)="onReturn()">
        <img [ngClass]="navBtn.disabled ? 'blg-button-disabled' : null" src="../../../assets/images/backbutton.png"
          alt="add" width="40px" height="40px">
      </button>
      <span>{{title | async}} Sales Invoice</span>
    </div>
    <div fxFlex="1 0 25" fxLayout="row" fxLayoutAlign="end" fxLayoutGap="5px">
      <button mat-raised-button color="primary" type="button" *ngIf="showClone()" [disabled]="disableCloneBtn$ | async" (click)="clone()">CLONE</button>
      <button mat-raised-button color="primary" type="button" [disabled]="disableFinal()" (click)="onFinal()" *ngIf="showFinal()">FINAL</button>
      <!-- <button mat-raised-button color="primary" type="button" *ngIf="showVoid()" (click)="onVoid()">VOID</button> -->
      <button mat-raised-button color="primary" type="button" [disabled]="disableSave()"
        (click)="onSave()" *ngIf="!appletSettings.HIDE_GENDOC_SAVE_BUTTON">SAVE</button>
    </div>
  </div>
</mat-card-title>
<br>
<mat-tab-group mat-stretch-tabs [dynamicHeight]="true" [selectedIndex]="selectedIndex$ | async" *ngIf="!showPanels()">
  <ng-container *ngFor="let orderedTab of getFilteredPanels(draft$ | async)">
    <!-- Eager Loaded Mat Tabs -->
    <mat-tab [label]="orderedTab.title" *ngIf="!orderedTab.lazy">
      <ng-template mat-tab-label>
        <span [style.color]="orderedTab?.invalid ? 'red' : 'inherit'" [style.font-weight]="orderedTab?.invalid ? 'bold' : 'normal'">
          {{ orderedTab.title }}
        </span>
      </ng-template>

      <ng-container [ngSwitch]="orderedTab.content">
        <ng-container *ngSwitchCase="'main-details'">
          <app-sales-invoice-main-details [draft$]="draft$"
          (updateMain)="onUpdateMain($event)" [appletSettings$]="appletSettings$"
            (selectSalesAgent)="goToSelectSalesAgent()" (selectMember)="goToSelectMember()"
            [pns$]="pns$" (formStatusChange)="onFormStatusChange($event)">
          </app-sales-invoice-main-details>
          <div style="padding: 5px;">
            <button mat-raised-button color="warn" type="button" (click)="onDiscard()" *ngIf="showDiscard()">DISCARD</button>
            <button mat-raised-button color="warn" type="button" (click)="onVoid()" *ngIf="showVoid()">VOID</button>
          </div>
        </ng-container>

        <app-sales-invoice-account *ngSwitchCase="'account'" [draft$]="draft$" [selectedIndex]="accountSelectedIndex$ | async"
          (selectEntity)="goToSelectEntity()" (selectVehicle) = "goToSelectVehicle()" (selectBilling)="goToSelectBilling()"
          (selectShipping)="goToSelectShipping()" (selectShippingEntity)="goToSelectShippingEntity()" (updateShipTo)="onUpdateShippingInfo($event)"
          (updateShippingAddress)="onUpdateShippingAddress($event)" (updateBillTo)="onUpdateBillingInfo($event)"
          (updateBillingAddress)="onUpdateBillingAddress($event)" (accountStatusChange)="onAccountStatusChange($event)"
          [appletSettings$]="appletSettings$"></app-sales-invoice-account>

        <app-sales-invoice-line-item-listing *ngSwitchCase="'lines'" [localState]="localState$ | async" [draft$]="draft$"
          (itemCreate)="goToLineItemCreate()" (itemEdit)="goToLineItemEdit($event)"></app-sales-invoice-line-item-listing>

        <app-sales-invoice-delivery-trips *ngSwitchCase="'delivery-trips'" [draft$]="draft$"></app-sales-invoice-delivery-trips>

        <app-sales-invoice-payment-listing *ngSwitchCase="'settlement'" [localState]="localState$ | async" [draft$]="draft$"
          (paymentCreate)="goToPaymentCreate()" (paymentEdit)="goToPaymentEdit($event)"></app-sales-invoice-payment-listing>

        <app-sales-invoice-department *ngSwitchCase="'department-hdr'" [draft$]="draft$" (updateDepartment)="onUpdateDepartmentInfo($event)">
        </app-sales-invoice-department>

        <app-sales-invoice-posting *ngSwitchCase="'posting'" [draft$]="draft$" (updatePosting)="onUpdatePostingInfo($event)"></app-sales-invoice-posting>

        <app-generic-document-posting *ngSwitchCase="'trace-document'" [genericDoc$]="hdr$"></app-generic-document-posting>
        <app-gross-profit-listing *ngSwitchCase="'gross-profit'"></app-gross-profit-listing>
      </ng-container>
    </mat-tab>

    <!-- Lazy Loaded Mat Tabs -->
    <mat-tab [label]="orderedTab.title" *ngIf="orderedTab.lazy">
      <ng-template matTabContent>
        <ng-container [ngSwitch]="orderedTab.content">
          <app-search-documents *ngSwitchCase="'search-documents'" [orientation]="orientation"
            [selectedIndex]="selectedSearchIndex$ | async"></app-search-documents>

          <app-e-invoice *ngSwitchCase="'e-invoice'"></app-e-invoice>

          <app-delivery-details *ngSwitchCase="'delivery-details'" [draft$]="draft$" [rowData]="pns$ | async"
            (selectDeliveryEntity)="goToSelectDeliveryEntity()" [selectedIndex]="deliveryDetailSelectedIndex$ | async"></app-delivery-details>

          <app-import-knock-off *ngSwitchCase="'ko-for'"></app-import-knock-off>

          <app-sales-invoice-arap *ngSwitchCase="'arap'" [draft$]="draft$"></app-sales-invoice-arap>

          <app-sales-invoice-contra-listing *ngSwitchCase="'contra'" [draft$]="draft$" [draft$]="draft$"
            [localState]="localState$ | async" [appletSettings]="appletSettings" (contraCreate)="goToContraCreate()"
            (contraEdit)="goToContraEdit($event)" ></app-sales-invoice-contra-listing>

          <app-sales-invoice-header-doc-link *ngSwitchCase="'doc-link'" [pns$]="pns$"></app-sales-invoice-header-doc-link>

          <app-sales-invoice-export *ngSwitchCase="'export'" [draft$]="draft$"></app-sales-invoice-export>

          <app-sales-invoice-attachments-listing *ngSwitchCase="'attachments'" [localState]="localState$ | async"
            (addAttachment)="goToAddAttachments()"></app-sales-invoice-attachments-listing>
        </ng-container>
      </ng-template>
    </mat-tab>
  </ng-container>
</mat-tab-group>

<ng-container *ngIf="showPanels()">
  <div class="scrollable-container">
  <mat-accordion [multi]="true">
    <mat-expansion-panel *ngFor="let panel of getFilteredPanels(draft$ | async); let i = index"
                         #panel
                         [expanded]="appletSettings[panel.expandSetting] || (i === expandedPanelIndex)"
                         (opened)="onPanelOpened(i)"
                         [ngClass]="{'expanded-panel': expandedPanelIndex === i}">
                         <mat-expansion-panel-header>
                          <mat-panel-title>
                            <span [style.color]="panel?.invalid ? 'red' : 'inherit'" [style.font-weight]="panel?.invalid ? 'bold' : 'normal'">
                              {{ panel.title }}
                            </span>
                          </mat-panel-title>
                        </mat-expansion-panel-header>

    <!-- Search Documents Panel Content -->
    <ng-container *ngIf="panel.content === 'search-documents'">
      <app-search-documents [orientation]="orientation"></app-search-documents>
    </ng-container>

    <!-- Main Details Panel Content -->
    <ng-container *ngIf="panel.content === 'main-details'">
      <app-sales-invoice-main-details [draft$]="draft$" (updateMain)="onUpdateMain($event)" [appletSettings$]="appletSettings$"
      (selectSalesAgent)="goToSelectSalesAgent()" (selectVehicle) = "goToSelectVehicle()" (selectMember)="goToSelectMember()" [pns$]="pns$"
      [isInExpansionPanel]="true" (formStatusChange)="onFormStatusChange($event)"></app-sales-invoice-main-details>
      <button mat-raised-button color="warn" type="button" (click)="onDiscard()" *ngIf="showDiscard()">DISCARD</button>
    </ng-container>

    <!-- E-invoice Panel Content -->
    <ng-container *ngIf="panel.content === 'e-invoice'">
      <app-e-invoice [orientation]="orientation"></app-e-invoice>
    </ng-container>

    <!--Account Panel Content -->
    <ng-container *ngIf="panel.content === 'account'">
      <app-sales-invoice-account [draft$]="draft$" [selectedIndex]="accountSelectedIndex$ | async"
      (selectEntity)="goToSelectEntity()" (selectBilling)="goToSelectBilling()" (selectShipping)="goToSelectShipping()"
      (updateShipTo)="onUpdateShippingInfo($event)" (updateShippingAddress)="onUpdateShippingAddress($event)"
      (updateBillTo)="onUpdateBillingInfo($event)" (updateBillingAddress)="onUpdateBillingAddress($event)"
      [orientation]="orientation" (accountStatusChange)="onAccountStatusChange($event)" [appletSettings$]="appletSettings$">
    </app-sales-invoice-account>
    </ng-container>

    <!--Lines Panel Content -->
    <ng-container *ngIf="panel.content === 'lines'">
      <app-sales-invoice-line-item-listing [localState]="localState$ | async" [draft$]="draft$"
      (itemCreate)="goToLineItemCreate()" (itemEdit)="goToLineItemEdit($event)"></app-sales-invoice-line-item-listing>
    </ng-container>

    <!--Delivery Details Panel Content -->
    <ng-container *ngIf="panel.content === 'delivery-details'">
      <app-delivery-details [draft$]="draft$" [rowData]="pns$ | async" (selectDeliveryEntity)="goToSelectDeliveryEntity()"
      [selectedIndex]="deliveryDetailSelectedIndex$ | async" [orientation]="orientation"></app-delivery-details>
    </ng-container>

    <!--KO For Panel Content -->
    <ng-container *ngIf="panel.content === 'ko-for' && !appletSettings.HIDE_KO_FOR_TAB">
      <ng-template matExpansionPanelContent>
        <app-import-knock-off [orientation]="orientation"></app-import-knock-off>
      </ng-template>
    </ng-container>

    <!--ARAP Panel Content -->
    <ng-container *ngIf="panel.content === 'arap'">
      <ng-template matExpansionPanelContent>
        <app-sales-invoice-arap [draft$]="draft$"></app-sales-invoice-arap>
      </ng-template>
    </ng-container>

    <!--Delivery Trips Panel Content -->
    <ng-container *ngIf="panel.content === 'delivery-trips'">
      <app-sales-invoice-delivery-trips [draft$]="draft$"></app-sales-invoice-delivery-trips>
    </ng-container>

    <!--Settlement Panel Content -->
    <ng-container *ngIf="panel.content === 'settlement'">
      <app-sales-invoice-payment-listing [localState]="localState$ | async" [draft$]="draft$"
    (paymentCreate)="goToPaymentCreate()" (paymentEdit)="goToPaymentEdit($event)"></app-sales-invoice-payment-listing>
    </ng-container>

    <!--Department Hdr Panel Content -->
    <ng-container *ngIf="panel.content === 'department-hdr'">
      <app-sales-invoice-department [draft$]="draft$" (updateDepartment)="onUpdateDepartmentInfo($event)">
      </app-sales-invoice-department>
    </ng-container>

    <!--Posting Panel Content -->
    <ng-container *ngIf="panel.content === 'posting'">
      <app-sales-invoice-posting [draft$]="draft$" (updatePosting)="onUpdatePostingInfo($event)">
      </app-sales-invoice-posting>
    </ng-container>

    <!--Trace Document Panel Content -->
    <ng-container *ngIf="panel.content === 'trace-document'  && !appletSettings.HIDE_TRACE_DOCUMENT_TAB">
      <app-generic-document-posting [genericDoc$]="hdr$" [orientation]="orientation">
      </app-generic-document-posting>
    </ng-container>

    <!--Contra Panel Content -->
    <ng-container *ngIf="panel.content === 'contra'">
      <ng-template matExpansionPanelContent>
        <app-sales-invoice-contra-listing [draft$]="draft$" [localState]="localState$ | async" [appletSettings]="appletSettings"
          (contraCreate)="goToContraCreate()" (contraEdit)="goToContraEdit($event)" ></app-sales-invoice-contra-listing>
      </ng-template>
    </ng-container>

    <!--Doc Link Panel Content -->
    <ng-container *ngIf="panel.content === 'doc-link'">
      <ng-template matExpansionPanelContent>
        <app-sales-invoice-header-doc-link [pns$]="pns$" [orientation]="orientation"></app-sales-invoice-header-doc-link>
      </ng-template>
    </ng-container>

    <!--export Panel Content -->
    <ng-container *ngIf="panel.content === 'export'">
      <app-sales-invoice-export [draft$]="draft$"></app-sales-invoice-export>
    </ng-container>

    <!--Attachments Panel Content -->
    <ng-container *ngIf="panel.content === 'attachments'">
      <app-sales-invoice-attachments-listing [localState]="localState$ | async" (addAttachment)="goToAddAttachments()">
      </app-sales-invoice-attachments-listing>
    </ng-container>
  </mat-expansion-panel>
</mat-accordion>
</div>
</ng-container>
