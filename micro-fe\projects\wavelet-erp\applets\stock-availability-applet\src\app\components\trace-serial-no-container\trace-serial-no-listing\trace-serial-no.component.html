<div class="view-col-table no-tab" fxLayout="column">
  <mat-card-title class="column-title">
    <div style="margin-top: 5px">{{compName}}</div>
  </mat-card-title>

<form [formGroup]="form">
  <div fxLayout="row wrap" fxFlexAlign="left" fxLayoutAlign=" center" fxLayoutGap="10px">
    <div fxFlex.gt-sm="20" fxFlex.gt-xs="20" fxFlex="100">
      <mat-form-field appearance="outline">
        <mat-label>Exact Match</mat-label>
        <input matInput [formControl]="form.controls['docNo']" type="text" autocomplete="off">
      </mat-form-field>
    </div>
    <div fxFlex.gt-sm="20" fxFlex.gt-xs="20" fxFlex="100">
      <mat-form-field appearance="outline">
        <mat-label>Keyword Search</mat-label>
        <input matInput [formControl]="form.controls['docNoKeyword']" type="text" autocomplete="off">
      </mat-form-field>
    </div>
    <div fxFlex.gt-sm="15" fxFlex.gt-xs="15" fxFlex="100">
      <mat-form-field appearance="outline">
        <mat-label>Date From</mat-label>
        <input matInput [matDatepicker]="start_modified_range"
          [formControl]="form.controls['from']" readonly (click)="start_modified_range.open()" />
        <mat-datepicker-toggle matSuffix [for]="start_modified_range"></mat-datepicker-toggle>
        <mat-datepicker touchUi="true" #start_modified_range></mat-datepicker>
      </mat-form-field>
    </div>
    <div fxFlex.gt-sm="15" fxFlex.gt-xs="15" fxFlex="100">
      <mat-form-field appearance="outline">
        <mat-label>Date To</mat-label>
        <input matInput [matDatepicker]="end_modified_range"
          [formControl]="form.controls['to']" readonly (click)="end_modified_range.open()" />
        <mat-datepicker-toggle matSuffix [for]="end_modified_range"></mat-datepicker-toggle>
        <mat-datepicker touchUi="true" #end_modified_range></mat-datepicker>
      </mat-form-field>
    </div>
    <div fxFlex.gt-sm="10" fxFlex.gt-xs="10" fxFlex="100">
      <button mat-raised-button color="primary" type="submit" (click)="onSearch()">SEARCH</button>
    </div>
    <div fxFlex.gt-sm="10" fxFlex.gt-xs="10" fxFlex="100">
      <button mat-raised-button color="primary" type="submit" (click)="onReset()">RESET</button>
    </div>
  </div>
</form>

  <div style="height: 100%;">
      <app-ag-grid-custom #gridWrapper [id]="compId+'Listing'" [title]="compName" [showColumns]="showColumns"
                          [getRowStyle]="getRowStyle" [columnDefs]="columnsDefs" [gridOptions]="gridOptions"
                          (gridReady)="onGridReady($event)">
      </app-ag-grid-custom>
  </div>

</div>
