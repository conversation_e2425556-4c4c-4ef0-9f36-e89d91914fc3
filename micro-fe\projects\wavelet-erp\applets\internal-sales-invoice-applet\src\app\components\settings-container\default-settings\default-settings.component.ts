import { ChangeDetectionStrategy, Component, On<PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { Store } from '@ngrx/store';
import { BranchContainerModel, FinancialItemService, GuidDataFieldInterface, LocationContainerModel, Pagination } from 'blg-akaun-ts-lib';
import { SessionActions } from 'projects/shared-utilities/modules/session/session-controller/actions';
import { SessionSelectors } from 'projects/shared-utilities/modules/session/session-controller/selectors';
import { SessionStates } from 'projects/shared-utilities/modules/session/session-controller/states';
import { AppConfig } from 'projects/shared-utilities/visa';
import { SubSink } from 'subsink2';
import { AppletSettings, DEFAULTS } from '../../../models/applet-settings.model';
import { PermissionStates } from 'projects/shared-utilities/modules/permission/permission-controller';
import { debounceTime, distinctUntilChanged } from 'rxjs/operators';
import { UserPermInquirySelectors } from 'projects/shared-utilities/modules/permission/user-permissions-inquiry-controller/selectors';
import { SalesInvoiceStates } from '../../../state-controllers/sales-invoice-controller/store/states';
import { ToastrService } from 'ngx-toastr';
import { SalesInvoiceActions } from '../../../state-controllers/sales-invoice-controller/store/actions';
import { CdkDragDrop, moveItemInArray } from '@angular/cdk/drag-drop';

@Component({
  selector: 'app-default-settings',
  templateUrl: './default-settings.component.html',
  styleUrls: ['./default-settings.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class DefaultSettingsComponent implements OnInit, OnDestroy {
  private subs = new SubSink();
  form: FormGroup;
  apiVisa = AppConfig.apiVisa;
  selectedBranch: GuidDataFieldInterface;
  branchGuids: any[];
  locationGuids: any[];
  detailsTabs = [
    { title: 'Search Document', content: 'search-documents' },
    { title: 'Main Details', content: 'main-details' },
    { title: 'E-Invoice', content: 'e-invoice' },
    { title: 'Account', content: 'account' },
    { title: 'Lines', content: 'lines' },
    { title: 'Delivery Details', content: 'delivery-details' },
    { title: 'KO For', content: 'ko-for' },
    { title: 'ARAP', content: 'arap' },
    { title: 'Delivery Trips', content: 'delivery-trips' },
    { title: 'Settlement', content: 'settlement' },
    { title: 'Department Hdr', content: 'department-hdr' },
    { title: 'Posting', content: 'posting' },
    { title: 'TraceDocument', content: 'trace-document' },
    { title: 'Contra', content: 'contra' },
    { title: 'Doc Link', content: 'doc-link' },
    { title: 'Export', content: 'export' },
    { title: 'Attachments', content: 'attachments' },
    { title: 'Gross Profit', content: 'gross-profit' }
  ];

  userPermissionTarget$ = this.permissionStore.select(UserPermInquirySelectors.selectUserPermInquiry);

  constructor(
    private readonly siStore: Store<SalesInvoiceStates>,
    private toastr: ToastrService,
    private fiService: FinancialItemService,
    private readonly store: Store<SessionStates>,
    protected readonly permissionStore: Store<PermissionStates>
  ) { }

  ngOnInit() {
    this.form = new FormGroup({
      DEFAULT_BRANCH: new FormControl(),
      DEFAULT_LOCATION: new FormControl(),
      DEFAULT_COMPANY: new FormControl(),
      DEFAULT_PRICEBOOK: new FormControl(),
      PRICING_RETAIL_GUID: new FormControl(),
      DEFAULT_DECIMAL_PRECISION: new FormControl(DEFAULTS.DECIMAL_PRECISION, [Validators.min(2), Validators.max(4)]),
      DEFAULT_DECIMAL_STEP: new FormControl((1 / Math.pow(10, DEFAULTS.DECIMAL_PRECISION)).toString().substring(1)),
    });
    this.subs.sink = this.store.select(SessionSelectors.selectMasterSettings).subscribe({
      next: (resolve: any) => {
        this.form.patchValue({
          DEFAULT_BRANCH: resolve?.DEFAULT_BRANCH,
          DEFAULT_LOCATION: resolve?.DEFAULT_LOCATION,
          DEFAULT_COMPANY: resolve?.DEFAULT_COMPANY,
          DEFAULT_PRICEBOOK: resolve?.DEFAULT_PRICEBOOK,
          PRICING_RETAIL_GUID: resolve?.PRICING_RETAIL_GUID,
          DEFAULT_DECIMAL_PRECISION: resolve?.DEFAULT_DECIMAL_PRECISION ?? DEFAULTS.DECIMAL_PRECISION,
          DEFAULT_DECIMAL_STEP: resolve?.DEFAULT_DECIMAL_STEP ?? (1 / Math.pow(10, resolve?.DEFAULT_DECIMAL_PRECISION ?? 2)).toFixed(resolve?.DEFAULT_DECIMAL_PRECISION ?? 2).substring(1),
        });

        if (resolve.DETAILS_TAB_ORDER) this.detailsTabs = [...resolve.DETAILS_TAB_ORDER];
      }
    });

    this.subs.sink = this.userPermissionTarget$.subscribe((targets) => {
      let target = targets.filter(
        (target) =>
          target.permDfn === "TNT_API_DOC_INTERNAL_SALES_INVOICE_READ_TGT_GUID"
      );
      let adminCreatePermissionTarget = targets.filter(
        (target) => target.permDfn === "TNT_TENANT_ADMIN"
      );
      let ownerCreatePermissionTarget = targets.filter(
        (target) => target.permDfn === "TNT_TENANT_OWNER"
      );
      if (adminCreatePermissionTarget[0]?.hasPermission || ownerCreatePermissionTarget[0]?.hasPermission) {
        this.branchGuids = [];
      } else {
        this.branchGuids = (target[0]?.target !== null && Object.keys(target[0]?.target || {}).length !== 0) ? target[0]?.target["bl_fi_mst_branch"] : [];
      }
    });

    this.subs.sink = this.form.valueChanges
      .pipe(debounceTime(100), distinctUntilChanged())
      .subscribe({
        next: (form) => {
          this.selectedBranch = form.DEFAULT_BRANCH;
        },
      });
  }

  onBranchSelected(e: BranchContainerModel) {
    this.selectedBranch = e.bl_fi_mst_branch.guid;
    this.form.patchValue({ DEFAULT_COMPANY: e.bl_fi_mst_branch.comp_guid });
    if (e.bl_fi_mst_branch_ext.find(x => x.param_code === "MAIN_LOCATION")) {
      this.form.controls['DEFAULT_LOCATION'].setValue(e.bl_fi_mst_branch_ext.find(x => x.param_code === "MAIN_LOCATION")?.value_string);
    }
  }

  onSave() {
    this.form.patchValue({
      DEFAULT_DECIMAL_STEP: (1 / Math.pow(10, this.form.value.DEFAULT_DECIMAL_PRECISION)).toString().substring(1),
    });
    const payload = {
      ...this.form.value,
      DETAILS_TAB_ORDER: this.detailsTabs,
    }
    this.store.dispatch(SessionActions.saveMasterSettingsInit({ settings: payload }));
  }

  onReset() {
    this.store.dispatch(
      SessionActions.saveMasterSettingsInit({
        settings: {
          DEFAULT_BRANCH: null,
          DEFAULT_LOCATION: null,
          DEFAULT_COMPANY: null,
          PRICING_RETAIL_GUID: null,
          DEFAULT_DECIMAL_PRECISION: DEFAULTS.DECIMAL_PRECISION, // default decimal precision
          DEFAULT_DECIMAL_STEP: DEFAULTS.DECIMAL_STEP,
          DETAILS_TAB_ORDER: this.detailsTabs,
        },
      })
    );
  }

  drop(event: CdkDragDrop<string[]>) {
    moveItemInArray(this.detailsTabs, event.previousIndex, event.currentIndex);
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }

}
