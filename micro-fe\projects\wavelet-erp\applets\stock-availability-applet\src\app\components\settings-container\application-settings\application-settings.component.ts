import { Component, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { FormControl, FormGroup } from "@angular/forms";
import { Store } from "@ngrx/store";
import { SessionActions } from 'projects/shared-utilities/modules/session/session-controller/actions';
import { SessionSelectors } from 'projects/shared-utilities/modules/session/session-controller/selectors';
import { SessionStates } from 'projects/shared-utilities/modules/session/session-controller/states';
import { SubSink } from "subsink2";
import { AppletSettings } from '../../../models/applet-settings.model';
import { AppConfig } from 'projects/shared-utilities/visa';
import { menuItems } from '../../../models/menu-items'

@Component({
  selector: 'app-application-settings',
  templateUrl: './application-settings.component.html',
  styleUrls: ['./application-settings.component.css']
})

export class ApplicationSettingsComponent implements OnInit, OnDestroy {
  private subs = new SubSink();

  form: FormGroup;
  apiVisa = AppConfig.apiVisa;
  maxCategories = 20;
  categoryArray = Array.from({ length: this.maxCategories }, (_, index) => index + 1);
  priceMetrics = [
    { code: 'PURCHASE_UNIT_PRICE', guid: 'PURCHASE_UNIT_PRICE' },
    { code: 'SALES_UNIT_PRICE', guid: 'SALES_UNIT_PRICE' },
    { code: 'SALES_MAX_PRICE', guid: 'SALES_MAX_PRICE' },
    { code: 'SALES_MIN_PRICE', guid: 'SALES_MIN_PRICE' },
    { code: 'REPLACEMENT_UNIT_PRICE_EXCL_TAX', guid: 'REPLACEMENT_UNIT_PRICE_EXCL_TAX' },
    { code: 'DELTA_PRICE1_EXCL_TAX', guid: 'DELTA_PRICE1_EXCL_TAX' },
    { code: 'DELTA_PRICE2_EXCL_TAX', guid: 'DELTA_PRICE2_EXCL_TAX' },
    { code: 'DELTA_PRICE3_EXCL_TAX', guid: 'DELTA_PRICE3_EXCL_TAX' },
    { code: 'REF_PRICE1_EXCL_TAX', guid: 'REF_PRICE1_EXCL_TAX' },
    { code: 'REF_PRICE2_EXCL_TAX', guid: 'REF_PRICE2_EXCL_TAX' },
    { code: 'REF_PRICE3_EXCL_TAX', guid: 'REF_PRICE3_EXCL_TAX' },
    { code: 'REBATE_PRICE1_EXCL_TAX', guid: 'REBATE_PRICE1_EXCL_TAX' },
    { code: 'REBATE_PRICE2_EXCL_TAX', guid: 'REBATE_PRICE2_EXCL_TAX' },
    { code: 'REBATE_PRICE3_EXCL_TAX', guid: 'REBATE_PRICE3_EXCL_TAX' }
  ]

  settingsMain = [];

  settings = [
    { controlName: 'HIDE_LISTING_AVG_COST', label: 'HIDE_LISTING_AVG_COST' },
    { controlName: 'HIDE_LISTING_LAST_PURCHASE_COST', label: 'HIDE_LISTING_LAST_PURCHASE_COST' },
    { controlName: 'HIDE_LISTING_FIFO_COST', label: 'HIDE_LISTING_FIFO_COST' },
    { controlName: 'HIDE_LISTING_LIFO_COST', label: 'HIDE_LISTING_LIFO_COST' },
    { controlName: 'HIDE_LISTING_PURCHASE_PRICE', label: 'HIDE_LISTING_PURCHASE_PRICE' },
    { controlName: 'HIDE_LISTING_SALES_PRICE', label: 'HIDE_LISTING_SALES_PRICE' },
    { controlName: 'HIDE_LISTING_SALES_MAX_PRICE', label: 'HIDE_LISTING_SALES_MAX_PRICE' },
    { controlName: 'HIDE_LISTING_SALES_MIN_PRICE', label: 'HIDE_LISTING_SALES_MIN_PRICE' },
    { controlName: 'HIDE_LISTING_REPLACEMENT_PRICE', label: 'HIDE_LISTING_REPLACEMENT_PRICE' },
    { controlName: 'HIDE_LISTING_REF_PRICE_1', label: 'HIDE_LISTING_REF_PRICE_1' },
    { controlName: 'HIDE_LISTING_REF_PRICE_2', label: 'HIDE_LISTING_REF_PRICE_2' },
    { controlName: 'HIDE_LISTING_REF_PRICE_3', label: 'HIDE_LISTING_REF_PRICE_3' },
    { controlName: 'HIDE_LISTING_DELTA_PRICE_1', label: 'HIDE_LISTING_DELTA_PRICE_1' },
    { controlName: 'HIDE_LISTING_DELTA_PRICE_2', label: 'HIDE_LISTING_DELTA_PRICE_2' },
    { controlName: 'HIDE_LISTING_DELTA_PRICE_3', label: 'HIDE_LISTING_DELTA_PRICE_3' },
    { controlName: 'HIDE_LISTING_REBATE_PRICE_1', label: 'HIDE_LISTING_REBATE_PRICE_1' },
    { controlName: 'HIDE_LISTING_REBATE_PRICE_2', label: 'HIDE_LISTING_REBATE_PRICE_2' },
    { controlName: 'HIDE_LISTING_REBATE_PRICE_3', label: 'HIDE_LISTING_REBATE_PRICE_3' },
    { controlName: 'ENABLE_FILTER_BY_TODAYS_TXN', label: 'ENABLE_FILTER_BY_TODAYS_TXN' },
    { controlName: 'HIDE_REPORT_DOC_SHORT_CODE', label: 'HIDE_REPORT_DOC_SHORT_CODE' },
    { controlName: 'HIDE_REPORT_UNIT_PRICE', label: 'HIDE_REPORT_UNIT_PRICE' },
    { controlName: 'HIDE_REPORT_UNIT_COST', label: 'HIDE_REPORT_UNIT_COST' },
    { controlName: 'HIDE_REPORT_INVENTORY_VALUE', label: 'HIDE_REPORT_INVENTORY_VALUE' },
    { controlName: 'HIDE_REPORT_AMOUNT_STD', label: 'HIDE_REPORT_AMOUNT_STD' },
    { controlName: 'HIDE_REPORT_AMOUNT_DISC', label: 'HIDE_REPORT_AMOUNT_DISC' },
    { controlName: 'HIDE_REPORT_AMOUNT_NET', label: 'HIDE_REPORT_AMOUNT_NET' },
    { controlName: 'HIDE_REPORT_AMOUNT_TAX', label: 'HIDE_REPORT_AMOUNT_TAX' },
    { controlName: 'HIDE_REPORT_AMOUNT_TXN', label: 'HIDE_REPORT_AMOUNT_TXN' },
  ];

  settings2 = [
    { controlName: 'HIDE_PURCHASE_GRN_PURCHASE_PRICE', label: 'HIDE_PURCHASE_GRN_PURCHASE_PRICE' },
    { controlName: 'HIDE_PURCHASE_GRN_SUPPLIER_NAME', label: 'HIDE_PURCHASE_GRN_SUPPLIER_NAME' },
    { controlName: 'INCREASE_ITEM_IMAGE_SIZE', label: 'INCREASE_ITEM_IMAGE_SIZE' },
    { controlName: 'HIDE_STOCK_MOVEMENT', label: 'HIDE_STOCK_MOVEMENT' },
  ]

  constructor(private readonly store: Store<SessionStates>) { }

  ngOnInit() {
    const controls = {};

    // Add menuItems-based toggles
    this.settingsMain = menuItems
      .filter(item => item.state) // skip tenant or non-state entries
      .map(item => ({
        label: item.name,
        controlName: this.stateToControlKey(item.state)
      }));

    menuItems.forEach(item => {
      if (item.state) {
        const key = this.stateToControlKey(item.state);
        controls[key] = new FormControl();
      }
    });         

    this.form = new FormGroup(controls);

    // main
    this.settingsMain.forEach(setting => {
      this.form.addControl(setting.controlName, new FormControl());
    });

    // listing
    this.settings.forEach(setting => {
      this.form.addControl(setting.controlName, new FormControl());
    });

    this.settings2.forEach(setting => {
      this.form.addControl(setting.controlName, new FormControl(true));
    });

    // category
    for (let i = 1; i <= this.maxCategories; i++) {
      this.form.addControl('ITEM_CATEGORY_GROUP_' + i, new FormControl());
    }

    this.form.addControl('PRICING_SCHEMES', new FormControl());
    this.form.addControl('PRICE_METRICS', new FormControl());
    
    this.subs.sink = this.store.select(SessionSelectors.selectMasterSettings).subscribe(resolve => {
      const patch: any = {};

      // Patch settings (columnFields)
      this.settings.forEach(field => {
        patch[field.controlName] = resolve?.[field.controlName];
      });

      // Patch settings2 (with default true if null)
      this.settings2.forEach(field => {
        const val = resolve?.[field.controlName];
        patch[field.controlName] = val === null ? true : val;
      });

      // Patch menuItems
      menuItems.forEach(item => {
        if (item.state) {
          const key = this.stateToControlKey(item.state);
          patch[key] = resolve?.[key];
        }
      });

      // Patch ITEM_CATEGORY_GROUP_1 to maxCategories
      for (let i = 1; i <= this.maxCategories; i++) {
        const key = 'ITEM_CATEGORY_GROUP_' + i;
        patch[key] = resolve?.[key];
      }

      // Patch pricing controls
      patch['PRICING_SCHEMES'] = resolve?.['PRICING_SCHEMES'];
      patch['PRICE_METRICS'] = resolve?.['PRICE_METRICS'];

      this.form.patchValue(patch);
    });
  }

  onSave() {
    this.store.dispatch(
      SessionActions.saveMasterSettingsInit({ settings: this.form.value })
    );
  }

  stateToControlKey(state: string) {
    return `HIDE_${state.replace(/-/g, '_').toUpperCase()}_MENU`;
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }

}
