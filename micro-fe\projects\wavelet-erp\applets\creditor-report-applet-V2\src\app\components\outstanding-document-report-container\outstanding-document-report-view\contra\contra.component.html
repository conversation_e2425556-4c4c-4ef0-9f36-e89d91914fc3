<div class="view-col-table" fxLayout="column">
   <div fxLayout="row wrap" fxLayoutAlign="end">
    <div class="blg-accent" fxFlex="1 0 25" fxLayout="row" fxLayoutAlign="space-between center">
      <button ngClass.xs="blg-button-mobile" #navBtn class="blg-button-icon" mat-button matTooltip="Create" type="button"
        (click)="onContra()" *ngIf="canContra()">
        <img [ngClass]="navBtn.disabled ? 'blg-button-disabled' : null" src="assets/images/add.png" alt="add" width="40px" height="40px">
      </button>
      <app-pagination fxFlex #pagination [agGridReference]="agGrid"></app-pagination>
    </div>
  </div>
    <div style="height: 100%;">
      <ag-grid-angular #agGrid
      style="height: 100%;"
      class="ag-theme-balham"
      rowSelection="single"
      rowModelType="serverSide"
      serverSideStoreType="partial"
      [getRowClass]="pagination.getRowClass"
      [columnDefs]="columnsDefs"
      [rowData]="rowData"
      [paginationPageSize]="pagination.rowPerPage"
      [animateRows]="true"
      [defaultColDef]="defaultColDef"
      [suppressRowClickSelection]="false"
      [sideBar]="true"
      (gridReady)="onGridReady($event)">
      </ag-grid-angular>
    </div>
  </div>
