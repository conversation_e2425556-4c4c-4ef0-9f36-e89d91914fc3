import { ChangeDetectionStrategy, Component, <PERSON><PERSON>hild, <PERSON><PERSON><PERSON>dren, <PERSON>ement<PERSON>ef, AfterViewChecked } from '@angular/core';
import { MatTabGroup } from '@angular/material/tabs';
import { MatExpansionPanel } from '@angular/material/expansion';
import { ComponentStore } from '@ngrx/component-store';
import { Store } from '@ngrx/store';
import { TenantAppletService, bl_fi_generic_doc_line_RowClass, bl_fi_generic_doc_link_RowClass, GenDocLinkContainerModel, GenericDocARAPContainerModel, GenericDocContainerModel, GenericDocLinkContainerModel, SalesInvoiceService, CompanyEntitySalesAvailableCreditLimitService } from 'blg-akaun-ts-lib';
import { ClientSidePermissionsSelectors } from 'projects/shared-utilities/modules/permission/client-side-permissions-controller/selectors';
import { ClientSidePermissionStates } from 'projects/shared-utilities/modules/permission/client-side-permissions-controller/states';
import { PermissionStates } from 'projects/shared-utilities/modules/permission/permission-controller';
import { UserPermInquirySelectors } from 'projects/shared-utilities/modules/permission/user-permissions-inquiry-controller/selectors';
import { SessionSelectors } from 'projects/shared-utilities/modules/session/session-controller/selectors';
import { SessionStates } from 'projects/shared-utilities/modules/session/session-controller/states';
import { ViewColumnComponent } from 'projects/shared-utilities/view-column.component';
import { AppConfig } from 'projects/shared-utilities/visa';
import { EMPTY, combineLatest, iif, of, Observable } from 'rxjs';
import { delay, distinctUntilChanged, map, mergeMap, take,tap,filter  } from 'rxjs/operators';
import { SubSink } from 'subsink2';
import { ViewColumnFacade } from '../../../facades/view-column.facade';
import { AppletSettings } from '../../../models/applet-settings.model';
import { BillingAddress, BillingInfo, ShippingAddress, ShippingInfo, SIDelivery, SIDepartment, SIMain, SIPosting } from '../../../models/sales-invoice.model';
import { HDRActions } from '../../../state-controllers/draft-controller/store/actions';
import { HDRSelectors, LinkSelectors, PNSSelectors } from '../../../state-controllers/draft-controller/store/selectors';
import { DraftStates } from '../../../state-controllers/draft-controller/store/states';
import { SalesInvoiceActions } from '../../../state-controllers/sales-invoice-controller/store/actions';
import { SalesInvoiceSelectors } from '../../../state-controllers/sales-invoice-controller/store/selectors';
import { SalesInvoiceStates } from '../../../state-controllers/sales-invoice-controller/store/states';
import { SalesInvoiceAccountComponent } from '../sales-invoice-create/account/account.component';
import { LineItemListingComponent } from '../sales-invoice-create/line-item/line-item-listing.component';
import { SalesInvoiceMainComponent } from '../sales-invoice-create/main-details/main-details.component';
import { MatDialog, MatDialogRef } from "@angular/material/dialog";
import { SalesInvoiceReturnPopUpComponent } from '../sales-invoice-create/return-popup/popup.component';
import { ToastrService } from 'ngx-toastr';
import { AkaunDiscardDialogComponent } from 'projects/shared-utilities/dialogues/akaun-discard-dialog/akaun-discard-dialog.component';
import { DeliveryDetailsComponent } from '../sales-invoice-create/delivery-details/delivery-details.component';
import { UtilitiesModule } from 'projects/shared-utilities/utilities/utilities.module';
import { SearchDocumentsComponent } from "../sales-invoice-create/search-documents/search-documents.component"
import { VehicleActions } from '../../../state-controllers/vehicle-controller/store/actions';
import { VehicleStates } from '../../../state-controllers/vehicle-controller/store/states';
import { VehicleSelectors } from '../../../state-controllers/vehicle-controller/store/selectors';
import { AkaunVoidDialogComponent } from "projects/shared-utilities/dialogues/akaun-void-dialog/akaun-void-dialog.component";

interface LocalState {
  deactivateReturn: boolean;
  deactivateAdd: boolean;
  deactivateList: boolean;
  selectedIndex: number;
  accountSelectedIndex: number;
  selectedLine: any;
  deleteConfirmation: boolean;
  deliveryDetailSelectedIndex: number;
  expandedPanel: number;
  selectedSearchIndex: number;
}

interface CreditLimit {
  entity_hdr_guid?: string;
  available_credit_limt?: number;
  new_available_credit_limt?: number;
  customer_code?: string;
  generic_doc_guids?: string[];
  generic_doc_hdr_server_docs?: string[];
}

interface creditLimitInputDto {
  entity_guids: string[];
  entity_credit_limit_guid: string;
  company_guid: string;
}

@Component({
  selector: 'app-sales-invoice-edit',
  templateUrl: './sales-invoice-edit.component.html',
  styleUrls: ['./sales-invoice-edit.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [ComponentStore]
})
export class SalesInvoiceEditComponent extends ViewColumnComponent implements AfterViewChecked {

  protected subs = new SubSink();

  protected compName = 'Sales Invoice Edit';
  protected readonly index = 2;
  protected localState: LocalState;
  protected prevLocalState: any;

  readonly localState$ = this.viewColFacade.selectLocalState(this.index);
  readonly deactivateReturn$ = this.componentStore.select(state => state.deactivateReturn);
  readonly selectedIndex$ = this.componentStore.select(state => state.selectedIndex);
  readonly selectedSearchIndex$ = this.componentStore.select(state => state.selectedSearchIndex);
  readonly accountSelectedIndex$ = this.componentStore.select(state => state.accountSelectedIndex);
  readonly deleteConfirmation$ = this.componentStore.select(state => state.deleteConfirmation);
  readonly deliveryDetailSelectedIndex$ = this.componentStore.select(state => state.deliveryDetailSelectedIndex);
  readonly expandedPanel$ = this.componentStore.select(state => state.expandedPanel);
  draft$ = this.draftStore.select(HDRSelectors.selectHdr);
  pns$ = this.draftStore.select(PNSSelectors.selectAll);
  hdr$ = this.store.select(SalesInvoiceSelectors.selectInvoice);
  link$ = this.draftStore.select(LinkSelectors.selectAll).subscribe(links => {
    this.links = links;
  });
  genDocLock$ = this.store.select(SalesInvoiceSelectors.selectGenDocLock);
  disableCloneBtn$ = this.store.select(SalesInvoiceSelectors.selectDisableCloneBtn);
  // payment$ = this.draftStore.select(PaymentSelectors.selectAll);
  userPermissionTarget$ = this.permissionStore.select(
    UserPermInquirySelectors.selectUserPermInquiry
  );

  prevIndex: number;
  apiVisa = AppConfig.apiVisa;
  viewMode: string;
  deleteConfirmation: boolean;
  links: bl_fi_generic_doc_link_RowClass[] = [];
  postingStatus;
  status;
  showDeleteButton = false;
  appletSettings: AppletSettings;
  clientSidePermissionSettings: any;
  SHOW_FINAL_BUTTON: boolean;
  SHOW_DISCARD_BUTTON: boolean;
  SHOW_VOID_BUTTON: boolean;
  HIDE_CLONE_BUTTON: boolean;
  ALLOW_CREDIT_LIMIT_FILTERING: boolean = false;
  hdrGuid;
  hdr: any;
  selectedHdr;
  editedInvoice: boolean;
  entityCreditLimitCheck: CreditLimit [] = [];
  genDocLock: boolean;
  creditLimitInputDto: creditLimitInputDto[] = [];
  akaunVoidDialogComponent: MatDialogRef<AkaunVoidDialogComponent>;

  //  ------------------------------------------ Expansion Panel  (Vertical UI)  ------------------------------------------------------   //
  panels = [
    { title: 'Search Document', content: 'search-documents',hide: 'HIDE_SEARCH_BY_DOCUMENT_BUTTON', condition: { status: 'TEMP' }, lazy: true },
    { title: 'Main Details', content: 'main-details', expandSetting: 'EXPAND_MAIN_DETAILS', invalid: false, lazy: false },
    { title: 'E-Invoice', content: 'e-invoice', lazy: true },
    { title: 'Account', content: 'account', expandSetting: 'EXPAND_ACCOUNT', invalid: false, lazy: false },
    { title: 'Lines', content: 'lines', expandSetting: 'EXPAND_LINE_ITEMS', invalid: false, lazy: false },
    { title: 'Delivery Details', content: 'delivery-details', hide: 'HIDE_DELIVERY_DETAILS_TAB', expandSetting: 'EXPAND_DELIVERY_DETAILS', lazy: true },
    { title: 'KO For', content: 'ko-for', hide: 'HIDE_KO_FOR_TAB', condition: { status: 'TEMP' }, expandSetting: 'EXPAND_KO_FOR', show: 'ENABLE_MULTIPLE_KO', lazy: true },
    { title: 'ARAP', content: 'arap', hide: 'HIDE_MAIN_ARAP_TAB', expandSetting: 'EXPAND_MAIN_ARAP', lazy: true},
    { title: 'Delivery Trips', content: 'delivery-trips', hide: 'HIDE_DELIVERY_TRIPS_TAB', expandSetting: 'EXPAND_DELIVERY_TRIPS', lazy: false },
    { title: 'Settlement', content: 'settlement', hide: 'HIDE_SETTLEMENT_TAB', expandSetting: 'EXPAND_SETTLEMENT', lazy: false },
    { title: 'Department Hdr', content: 'department-hdr', hide: 'HIDE_DEPARTMENT_HDR_TAB', expandSetting: 'EXPAND_DEPARTMENT_HDR', lazy: false },
    { title: 'Posting', content: 'posting', hide: 'HIDE_POSTING_TAB', expandSetting: 'EXPAND_POSTING', lazy: false },
    { title: 'TraceDocument', content: 'trace-document', hide: 'HIDE_TRACE_DOCUMENT_TAB', expandSetting: 'EXPAND_TRACE_DOCUMENT', lazy: false },
    { title: 'Contra', content: 'contra', hide: 'HIDE_MAIN_CONTRA_TAB', expandSetting: 'EXPAND_MAIN_CONTRA', lazy: true },
    { title: 'Doc Link', content: 'doc-link', hide: 'HIDE_DOC_LINK_TAB', expandSetting: 'EXPAND_DOC_LINK', lazy: true },
    { title: 'Export', content: 'export', hide: 'HIDE_EXPORT_TAB', expandSetting: 'EXPAND_EXPORT', lazy: true },
    { title: 'Gross Profit', content: 'gross-profit', hide: 'HIDE_GROSS_PROFIT_TAB', expandSetting: 'EXPAND_GROSS_PROFIT', lazy: true },
    { title: 'Attachments', content: 'attachments', hide: 'HIDE_ATTACHMENT_TAB', expandSetting: 'EXPAND_ATTACHMENT', lazy: true }
  ];
  expandedPanelIndex: number = 0;
  orientation: boolean;
  resetExpansion: boolean = false;
  @ViewChildren('panel', { read: ElementRef }) panelScroll;
  isPanelExpanded: boolean = false;
  viewCheckedSub: boolean = false;

  //  -----------------------------------------------------------------------------------------------------------------------------------   //

  @ViewChild(MatTabGroup) matTab: MatTabGroup;
  @ViewChild(SearchDocumentsComponent) search: SearchDocumentsComponent;
  @ViewChild(SalesInvoiceAccountComponent) account: SalesInvoiceAccountComponent;
  @ViewChild(LineItemListingComponent) lines: LineItemListingComponent;
  @ViewChild(SalesInvoiceMainComponent) main: SalesInvoiceMainComponent;
  @ViewChild(DeliveryDetailsComponent) deliveryDetails: DeliveryDetailsComponent;
  akaunDiscardDialogComponent: MatDialogRef<AkaunDiscardDialogComponent>;
  hasUpdatePermission: boolean;
  eInvoiceEnabled = false;
  docNo: any;
  title: Observable<any> = of('Create');

  constructor(
    private viewColFacade: ViewColumnFacade,
    private readonly componentStore: ComponentStore<LocalState>,
    private appletService: TenantAppletService,
    protected readonly vehicleStore: Store<VehicleStates>,
    protected readonly draftStore: Store<DraftStates>,
    protected readonly sessionStore: Store<SessionStates>,
    protected readonly permissionStore: Store<PermissionStates>,
    private dialogRef: MatDialog,
    protected readonly store: Store<SalesInvoiceStates>,
    private siService: SalesInvoiceService,
    private toastr: ToastrService,
    private entitySalesAvailableCreditLimitService: CompanyEntitySalesAvailableCreditLimitService
    ) {
    super();
  }
  masterSettings$ = this.sessionStore.select(
    SessionSelectors.selectMasterSettings
  );
  clientSidePermissions$ = this.permissionStore.select(
    ClientSidePermissionsSelectors.selectAll
  );
  mainTabInvalid: boolean = false;
  accountTabInvalid: boolean = false;
  lineTabInvalid: boolean = false;
  ENABLE_VEHICLE_TAB: boolean;

  appletSettings$ = combineLatest([
    this.sessionStore.select(SessionSelectors.selectMasterSettings),
    this.sessionStore.select(SessionSelectors.selectPersonalSettings)
  ]).pipe(map(([a, b]) => ({...a, ...b})));

  ngOnInit() {
    this.subs.sink = this.genDocLock$.subscribe(lock=>{
      this.genDocLock = lock;
    })
    this.subs.sink = this.appletSettings$.subscribe({
      next: (resolve: AppletSettings) => {
        this.appletSettings = resolve;
        if (resolve?.DETAILS_TAB_ORDER) {
          const orderedMap: { [key: string]: number } = {};
          if (resolve.DETAILS_TAB_ORDER?.length > 0) {
            resolve.DETAILS_TAB_ORDER?.forEach((tab, index) => { orderedMap[tab.content] = index; });
            this.panels = [...this.panels].filter(panel => orderedMap.hasOwnProperty(panel.content))
              .sort((a, b) => (orderedMap[a.content] ?? Number.MAX_VALUE) - (orderedMap[b.content] ?? Number.MAX_VALUE));
          }
        }
      },
    });
    this.subs.sink = this.appletSettings$.subscribe((resolve: any) => {
      this.HIDE_CLONE_BUTTON = resolve?.HIDE_CLONE_BUTTON;
      this.showDeleteButton =  resolve?.SHOW_DOCUMENT_DELETE_BUTTON;
    });
    this.subs.sink = this.clientSidePermissions$.subscribe({
      next: (resolve) => {
        this.clientSidePermissionSettings = resolve;
        resolve.forEach(permission => {
          if (permission.perm_code === "SHOW_GENDOC_FINAL_BUTTON") {
            this.SHOW_FINAL_BUTTON = true;
          }

          if (permission.perm_code === "SHOW_GENDOC_DISCARD_BUTTON") {
            this.SHOW_DISCARD_BUTTON = true;
          }

          if (permission.perm_code === "SHOW_GENDOC_VOID_BUTTON") {
            this.SHOW_VOID_BUTTON = true;
          }

          if (permission.perm_code === "ALLOW_CREDIT_LIMIT_FILTERING") {
            this.ALLOW_CREDIT_LIMIT_FILTERING = true;
          }
        })
      }
    });
    this.subs.sink = this.localState$.subscribe(a => {
      this.localState = a;
      this.componentStore.setState(a);
    });
    this.subs.sink = this.viewColFacade.prevIndex$.subscribe(resolve => this.prevIndex = resolve);
    this.subs.sink = this.viewColFacade.prevLocalState$().subscribe(resolve => this.prevLocalState = resolve);
    this.subs.sink = this.deleteConfirmation$.pipe(
      mergeMap(a => {
        return iif(() => a, of(a).pipe(delay(3000)), of(EMPTY));
      })
    ).subscribe(resolve => {
      if (resolve === true) {
        this.componentStore.patchState({ deleteConfirmation: false });
        this.deleteConfirmation = false;
      }
    });
    this.subs.sink = this.hdr$
    .pipe(
      distinctUntilChanged((prev, curr) => JSON.stringify(prev) === JSON.stringify(curr)),
      map(resolve => {
        if (resolve && resolve.bl_fi_generic_doc_hdr) {
          this.hdr = resolve;
          this.docNo = resolve.bl_fi_generic_doc_hdr.server_doc_1;
          this.hdrGuid = resolve.bl_fi_generic_doc_hdr.guid;
          this.status = resolve.bl_fi_generic_doc_hdr.status;
          this.postingStatus = resolve.bl_fi_generic_doc_hdr.posting_status;


          this.title = resolve.bl_fi_generic_doc_hdr.status === 'TEMP' ? of('Create') : of('Edit');
        } else {
          console.warn('Unexpected resolve structure:', resolve);
        }
        return resolve;
      })
    )
    .subscribe({
      next: () => console.log('State updated successfully'),
      error: err => console.error('Error updating state:', err),
    });

    this.subs.sink = this.userPermissionTarget$.subscribe((targets) => {
      let updatePermissionTarget = targets.filter(
        (target) =>
          target.permDfn === "TNT_API_DOC_INTERNAL_SALES_INVOICE_UPDATE_TGT_GUID"
      );
      let adminUpdatePermissionTarget = targets.filter(
        (target) =>
          target.permDfn === "TNT_TENANT_ADMIN"
      );
      let ownerUpdatePermissionTarget = targets.filter(
        (target) =>
          target.permDfn === "TNT_TENANT_OWNER"
      );
      if (updatePermissionTarget[0]?.hasPermission || adminUpdatePermissionTarget[0]?.hasPermission || ownerUpdatePermissionTarget[0]?.hasPermission) {
        this.hasUpdatePermission = true
      } else {
        this.hasUpdatePermission = false
      }
    });

    this.draftStore.select(HDRSelectors.selectHdr).subscribe((value) => {
      this.selectedHdr = value;
    });

    this.store.select(SalesInvoiceSelectors.selecteditedInvoice).subscribe(resp => {
      this.editedInvoice = resp;
    });
    this.subs.sink = this.pns$.subscribe(pns =>{
      if(pns.length > 0) {
        this.highlightLineTab(false)
      }else{
        this.highlightLineTab(true)
      }
    })

    this.store.select(SalesInvoiceSelectors.selectEInvoiceEnabled).subscribe(e => {
      this.eInvoiceEnabled = e;
    });

    this.subs.sink = this.store.select(SalesInvoiceSelectors.selectResetExpansionPanel).subscribe(x => {
      this.resetExpansion = x;
      if (!this.resetExpansion){
        this.subs.sink = this.expandedPanel$.subscribe(expandedPanel =>{
          if(expandedPanel !== undefined) {
            this.expandedPanelIndex = expandedPanel;
        }});
      } else {
        this.expandedPanelIndex = 0;
        this.store.dispatch(SalesInvoiceActions.resetExpansionPanel({ resetIndex: null }));
      }
      this.initializeExpandedPanels();
    });
  }

  ngAfterViewChecked() {
    if(this.orientation){
      if (this.isPanelExpanded) {
        if(this.resetExpansion){
          this.expandedPanelIndex = 0;
          this.store.dispatch(SalesInvoiceActions.resetExpansionPanel({ resetIndex: null }));
        } else {
          if(!this.viewCheckedSub) {
            this.expandedPanel$.subscribe(expandedPanel => {
              if (expandedPanel !== undefined) { this.expandedPanelIndex = expandedPanel }
            });
            this.viewCheckedSub = true;
          }
        }
        setTimeout(() => this.panelScroll.toArray()[this.expandedPanelIndex].nativeElement.scrollIntoView({ behavior: 'smooth' }),500);;
        this.isPanelExpanded = false;  // Reset the flag after scrolling
      }
    }
  }

  onUpdateMain(form: SIMain) {
    this.draftStore.dispatch(HDRActions.updateMain({ form }));
  }

  onUpdateMainDelivery(form: SIDelivery) {
    this.draftStore.dispatch(HDRActions.updateMainDelivery({ form }));
  }

  onUpdateBillingInfo(form: BillingInfo) {
    this.draftStore.dispatch(HDRActions.updateBillingInfo({ form }));
  }

  onUpdateBillingAddress(form: BillingAddress) {
    this.draftStore.dispatch(HDRActions.updateBillingAddress({ form }));
  }

  onUpdateShippingInfo(form: ShippingInfo) {
    this.draftStore.dispatch(HDRActions.updateShippingInfo({ form }));

    if(this.appletSettings?.ENABLE_IMPORT_EXPORT){
      this.draftStore.dispatch(HDRActions.updateEInvoiceShippingInfo({ form }));
    }
  }

  onUpdateShippingAddress(form: ShippingAddress) {
    this.draftStore.dispatch(HDRActions.updateShippingAddress({ form }));

    if(this.appletSettings?.ENABLE_IMPORT_EXPORT){
      this.draftStore.dispatch(HDRActions.updateEInvoiceShippingAddress({ form }));
    }
  }

  onUpdateDepartmentInfo(form: SIDepartment) {
    this.draftStore.dispatch(HDRActions.updateDepartment({ form }));
  }

  onUpdatePostingInfo(form: SIPosting) {
    this.draftStore.dispatch(HDRActions.updatePosting({ form }));
  }

  onReset() {
    if (this.postingStatus !== 'FINAL' && this.status === 'ACTIVE') {
      this.viewColFacade.resetDraft();
    }
    else {
      this.viewColFacade.showFailedToast({ message: 'This document has been posted' });
    }
  }

  async onFinal() {
    if (!this.validateInput()){
      return;
    }
    this.subs.sink = this.draft$.subscribe(async (resolve) => {
      const json = {
        posting_status: "FINAL",
      };
      let entityGuids: string[] = [];
      let docsToFinal: GenericDocContainerModel[] = [];
      let temp: GenericDocContainerModel = {
        bl_fi_generic_doc_hdr: resolve,
        bl_fi_generic_doc_event: null,
        bl_fi_generic_doc_ext: null,
        bl_fi_generic_doc_link: null,
        bl_fi_generic_doc_line: null,
      };
      entityGuids.push(
        temp.bl_fi_generic_doc_hdr.doc_entity_hdr_guid.toString()
      );
      this.creditLimitInputDto.push({
        company_guid: temp.bl_fi_generic_doc_hdr.guid_comp.toString(),
        entity_credit_limit_guid: (<any>temp.bl_fi_generic_doc_hdr)
          .doc_entity_hdr_json?.creditLimitGuid,
        entity_guids: [
          temp.bl_fi_generic_doc_hdr.doc_entity_hdr_guid.toString(),
        ],
      });
      const transformedPayload = {
        creditLimitDto: this.creditLimitInputDto.map((item) => ({
          entity_guid: item.entity_guids[0], // Assuming there is only one entity_guid in the array
          company_guid: item.company_guid,
          credit_limit_guid: item.entity_credit_limit_guid,
        })),
      };

      docsToFinal.push(temp);
      if (resolve.posting_status !== "FINAL" && resolve.status === "ACTIVE") {

        if (this.appletSettings?.ENABLE_CREDIT_LIMIT_FILTER || this.ALLOW_CREDIT_LIMIT_FILTERING) {
          this.entityCreditLimitCheck = [];
          let creditLimitDTO = transformedPayload;
          this.subs.sink = await this.entitySalesAvailableCreditLimitService
            .post(creditLimitDTO, AppConfig.apiVisa)
            .subscribe((response) => {
              response.data.forEach((record) => {
                this.entityCreditLimitCheck.push({
                  available_credit_limt: record.available_credit_limit,
                  new_available_credit_limt: record.available_credit_limit,
                  entity_hdr_guid: record.entity_hdr_guid,
                  customer_code: record.customer_code,
                  generic_doc_guids: [],
                  generic_doc_hdr_server_docs: [],
                });
              });

              docsToFinal.forEach((doc) => {
                if (!this.entityCreditLimitCheck || this.entityCreditLimitCheck.length === 0) {
                  // No credit check records, allow finalization
                  this.store.dispatch(SalesInvoiceActions.editSalesInvoiceFinalInit());
                } else {
                  let matched = false;

                  this.entityCreditLimitCheck.forEach((record) => {
                    if (
                      record.new_available_credit_limt > 0 &&
                      doc.bl_fi_generic_doc_hdr.doc_entity_hdr_guid === record.entity_hdr_guid
                    ) {
                      matched = true;
                      this.store.dispatch(SalesInvoiceActions.editSalesInvoiceFinalInit());
                    } else if (
                      doc.bl_fi_generic_doc_hdr.doc_entity_hdr_guid === record.entity_hdr_guid
                    ) {
                      this.toastr.error(
                        "Customer " +
                        record.customer_code +
                        " - Available Credit Limit: " +
                        record.available_credit_limt,
                        "Error",
                        {
                          tapToDismiss: true,
                          progressBar: true,
                          timeOut: 0,
                          extendedTimeOut: 0,
                        }
                      );
                    }
                  });

                  // Optionally log if no match was found at all
                  if (!matched) {
                    console.log("No matching entity found for", doc.bl_fi_generic_doc_hdr.doc_entity_hdr_guid);
                  }
                }
              });
            });
        } else {
          this.store.dispatch(SalesInvoiceActions.editSalesInvoiceFinalInit());
        }
      } else {
        this.viewColFacade.showFailedToast({
          message: "This document has been posted",
        });
      }
    });
  }

  onVoid() {
    this.akaunVoidDialogComponent = this.dialogRef.open(AkaunVoidDialogComponent, { width: '400px' });
    this.akaunVoidDialogComponent.componentInstance.confirmMessage = 'Are you sure you want to void document ';
    this.akaunVoidDialogComponent.componentInstance.docNo = this.docNo;
    this.akaunVoidDialogComponent.afterClosed().subscribe((result) => {
      if(result === true) {
        this.subs.sink = this.draft$.subscribe(resolve => {
          const json = {
            posting_status: 'VOID'
          }
          let temp: GenericDocContainerModel = {
            bl_fi_generic_doc_hdr: resolve,
            bl_fi_generic_doc_event: null,
            bl_fi_generic_doc_ext: null,
            bl_fi_generic_doc_link: null,
            bl_fi_generic_doc_line: null
          };
          if (resolve.posting_status === 'FINAL' || (resolve.status !== 'ACTIVE' && resolve.status !== null)) {
            this.store.dispatch(SalesInvoiceActions.voidSalesInvoiceInit({ status: json, doc: temp }));
          } else {
            this.viewColFacade.showFailedToast({ message: 'This document has not been finalized yet' });
          }
        });
      }
    });
  }

  disableSave() {
    return this.main?.form.invalid ||
           this.account?.entity?.form.invalid ||
           this.status === 'TEMP' ||
           !this.hasUpdatePermission  || this.genDocLock;
  }


  onSave() {
    this.ENABLE_VEHICLE_TAB = this.appletSettings?.ENABLE_VEHICLE_TAB ?? false;
    if (this.ENABLE_VEHICLE_TAB){
      this.vehicleStore.select(VehicleSelectors.selectSelectedVehicle).pipe(take(1)).subscribe(data => {
        const vehicleHdr = data?.bl_fi_mst_entity_vehicle_hdr;
        if (!vehicleHdr?.entity_hdr_guid) {
          this.store.select(HDRSelectors.selectDocEntityHdrGuid).pipe(
            take(1),
            filter(entityGuid => !!entityGuid), // Ensures entityGuid is valid before dispatching
          ).subscribe(entityGuid => {
            this.vehicleStore.dispatch(
              VehicleActions.updateVehicleWithEntityId({
                guid: vehicleHdr.guid.toString(),
                entityGuid: entityGuid.toString()
              })
            );
          });
        }
      });
      this.vehicleStore.dispatch(VehicleActions.selectToggleMode({ SelectedToggleMode: false }));
    }
    if (!this.validateInput()){
      return;
    }

    if (this.hdr && this.hdr.base_doc_ccy !== this.hdr.doc_ccy
      && this.hdr.base_doc_xrate <= 0) {
        this.viewColFacade.showFailedToast({ message: 'The currency rate cannot be ZERO or negative.' });
        return;
    }
    else {

      this.setConfigToDraftGendoc();

      if(this.status === 'TEMP'){
        this.store.dispatch(SalesInvoiceActions.createSalesInvoicesInit());
      }else{
        this.store.dispatch(SalesInvoiceActions.editedInvoice({edited: false}));
        this.store.dispatch(SalesInvoiceActions.editSalesInvoiceInit());
      }
    }
  }

  // set the selected intercompany config in the draft
  setConfigToDraftGendoc(){
    if( this.account.intercompany !== null && this.account.intercompany !== undefined &&
      this.account.intercompany.selectedConfigs !== null && this.account.intercompany.selectedConfigs !== undefined ){
      this.store.dispatch(HDRActions.setBranchIntercompanySettingGuids({setting: this.account.intercompany.selectedConfigs }))
    }
  }

  onDelete() {
    if (this.deleteConfirmation) {
      this.store.dispatch(SalesInvoiceActions.deleteSalesInvoiceInit());
      this.deleteConfirmation = false;
      this.componentStore.patchState({ deleteConfirmation: false });
    } else {
      this.deleteConfirmation = true;
      this.componentStore.patchState({ deleteConfirmation: true });
    }
  }

  deleteButtonCondition() {
    if (this.postingStatus === "FINAL" || (this.status !== 'ACTIVE' && this.status !== null)) {
      return false;
    }
    else {
      return this.showDeleteButton;
    }
  }

  goToSelectEntity() {
    if (this.orientation) {
      this.expandedPanelIndex = 2;
      this.savePanelState();
    }
    this.viewColFacade.updateInstance(this.index, {
      ...this.localState,
      deactivateReturn: true,
      deactivateAdd: true,
      deactivateList: true
    });
    this.viewColFacade.updateInstance(55, {
      deactivateReturn: false,
      selectedIndex: 0
    });
    this.viewColFacade.onNextAndReset(this.index, 55);
  }

  goToSelectVehicle(){
    this.viewColFacade.updateInstance(this.index, {
      ...this.localState,
      deactivateReturn: true,
      deactivateAdd: true,
      deactivateList: true
    });
    this.viewColFacade.updateInstance(55, {
      deactivateReturn: false,
      selectedIndex: 1
    });
    this.viewColFacade.onNextAndReset(this.index, 55);
  }

  goToSelectDeliveryEntity() {
    if (this.orientation) {
      this.savePanelState();
    }
    this.viewColFacade.updateInstance(this.index, {
      ...this.localState,
      deactivateReturn: true,
      deactivateAdd: true,
      deactivateList: true
    });
    this.viewColFacade.onNextAndReset(this.index, 41);
  }

  goToSelectSalesAgent() {
    if (this.orientation) {
      this.savePanelState();
    }
    this.viewColFacade.updateInstance(this.index, {
      ...this.localState,
      deactivateReturn: true,
      deactivateAdd: true,
      deactivateList: true
    });
    this.viewColFacade.onNextAndReset(this.index, 3);
  }

  goToSelectMember() {
    if (this.orientation) {
      this.savePanelState();
    }
    this.viewColFacade.updateInstance(this.index, {
      ...this.localState,
      deactivateReturn: true,
      deactivateAdd: true,
      deactivateList: true
    });
    this.viewColFacade.onNextAndReset(this.index, 18);
  }

  goToSelectBilling() {
    if (this.orientation) {
      this.expandedPanelIndex = 2;
      this.savePanelState();
    }
    this.viewColFacade.updateInstance(this.index, {
      ...this.localState,
      deactivateReturn: true,
      deactivateAdd: true,
      deactivateList: true
    });
    this.viewColFacade.onNextAndReset(this.index, 5);
  }

  goToSelectShipping() {
    if (this.orientation) {
      this.expandedPanelIndex = 2;
      this.savePanelState();
    }
    this.viewColFacade.updateInstance(this.index, {
      ...this.localState,
      deactivateReturn: true,
      deactivateAdd: true,
      deactivateList: true
    });
    this.viewColFacade.onNextAndReset(this.index, 6);
  }

  goToSelectShippingEntity(){
  if (this.orientation) {
  this.expandedPanelIndex = 2;
  this.savePanelState();
  }
    this.viewColFacade.updateInstance(this.index, {
      ...this.localState,
      deactivateReturn: true,
      deactivateAdd: true,
      deactivateList: true
    });
    this.viewColFacade.onNextAndReset(this.index, 59);
  }

  goToLineItemCreate() {
    this.expandedPanelIndex = 3;
    if (this.orientation) {
      this.savePanelState();
    }
    this.store.dispatch(SalesInvoiceActions.selectMode({ mode: 'edit' }))
    this.viewColFacade.updateInstance(this.index, {
      ...this.localState,
      deactivateAdd: true,
      deactivateReturn: true,
      deactivateList: true
    });
    this.viewColFacade.onNextAndReset(this.index, 7);
  }

  goToLineItemEdit(item: bl_fi_generic_doc_line_RowClass) {
    if (item && !this.localState.deactivateList) {
      const lineItem = { ...item };
      this.store.dispatch(SalesInvoiceActions.selectLineItem({ lineItem }));
      if (this.orientation) {
        this.expandedPanelIndex = 3;
        this.savePanelState();
      }
      this.viewColFacade.updateInstance(this.index, {
        ...this.localState,
        deactivateAdd: true,
        deactivateReturn: true,
        deactivateList: false,
        selectedLine: item.guid
      });
      this.viewColFacade.onNextAndReset(this.index, 9);
    }
  }

  goToPaymentCreate() {
    if (this.orientation) {
      this.savePanelState();
    }
    this.viewColFacade.updateInstance(this.index, {
      ...this.localState,
      deactivateAdd: true,
      deactivateReturn: true,
      deactivateList: true
    });
    this.viewColFacade.onNextAndReset(this.index, 10);
  }

  goToPaymentEdit(payment: bl_fi_generic_doc_line_RowClass) {
    if (payment && !this.localState.deactivateList) {
      this.store.dispatch(SalesInvoiceActions.selectPayment({ payment }))
      if (this.orientation) {
        this.savePanelState();
      }
      this.viewColFacade.updateInstance(this.index, {
        ...this.localState,
        deactivateAdd: true,
        deactivateReturn: true,
        deactivateList: false,
        selectedLine: payment.guid
      });
      this.viewColFacade.onNextAndReset(this.index, 11);
    }
  }

  goToContraCreate() {
    this.store.dispatch(SalesInvoiceActions.refreshArapListing({refreshArapListing : false}));
    if (this.orientation) {
      this.savePanelState();
    }
    this.viewColFacade.updateInstance(this.index, {
      ...this.localState,
      deactivateAdd: true,
      deactivateReturn: true,
      deactivateList: true
    });
    this.viewColFacade.onNextAndReset(this.index, 14);
  }

  goToContraEdit(contra: GenericDocARAPContainerModel) {
    this.store.dispatch(SalesInvoiceActions.selectContraLink({ link: contra }));
    if (contra && !this.localState.deactivateList) {
      if (this.orientation) {
        this.savePanelState();
      }
      this.viewColFacade.updateInstance<LocalState>(this.index, {
        ...this.localState,
        deactivateReturn: true,
        deactivateAdd: false,
        deactivateList: false,
      });
      this.viewColFacade.onNextAndReset(this.index, 17);
    }
  }

  goToAddAttachments() {
    if (this.orientation) {
      this.savePanelState();
    }
    this.viewColFacade.updateInstance(this.index, {
      ...this.localState,
      deactivateAdd: true,
      deactivateReturn: true,
      deactivateList: true
    });
    this.viewColFacade.onNextAndReset(this.index, 15);
  }

  onReturn() {
    this.vehicleStore.dispatch(VehicleActions.selectToggleMode({ SelectedToggleMode: false }));

    if(!this.genDocLock){
      this.store.dispatch(SalesInvoiceActions.unlockDocument({hdrGuid: this.hdrGuid}));
    }

    if(this.postingStatus !== 'DISCARDED' && this.editedInvoice){
      this.dialogRef.open(SalesInvoiceReturnPopUpComponent);
    }
    else{
      this.store.dispatch(SalesInvoiceActions.refreshArapListing({refreshArapListing : true}));
      this.store.dispatch(SalesInvoiceActions.setEditMode({ editMode: false }));
      if (this.orientation) {
        this.savePanelState();
        this.store.dispatch(SalesInvoiceActions.resetExpansionPanel({ resetIndex: null }));
      }
      this.vehicleStore.dispatch(VehicleActions.selectVehicle({vehicle:null}))

      this.viewColFacade.updateInstance<LocalState>(this.prevIndex, {
        ...this.prevLocalState,
        deactivateAdd: false,
        deactivateList: false
      });
      this.viewColFacade.onPrev(this.prevIndex);
      this.store.dispatch(SalesInvoiceActions.resetDraft());
    }
  }

  disableFinal() {
    let result = false;
    this.subs.sink = this.pns$.subscribe({
      next: (pns: any) => {
        pns.forEach(p=>{
          if(p.item_sub_type==='SERIAL_NUMBER'){
            result = UtilitiesModule.checkSerialValid(<any> p.serial_no);
          }
          if (p.item_sub_type === 'BATCH_NUMBER') {
            const batches = p.batch_no?.batches || [];

            if (batches.length === 0) {
              result = true;
            } else {
              const totalBatchQty = batches
                .map((batch: { qty: number }) => batch.qty ? Number(batch.qty) : 0)
                .reduce((sum: number, qty: number) => sum + qty, 0);

              if (totalBatchQty !== p.quantity_base) {
                result = true;
              }
            }
          }
        })
      }
    });
    return result;
  }


  showFinal(): boolean {
    if(this.genDocLock){
      return false;
    }

    return (!this.appletSettings.HIDE_GENDOC_FINAL_BUTTON || this.SHOW_FINAL_BUTTON)
      && this.status === 'ACTIVE' && (!this.postingStatus || this.postingStatus==="DRAFT") ;
  }

  showDiscard(): boolean {
    if(this.genDocLock){
      return false;
    }

    return (!this.appletSettings.HIDE_GENDOC_DISCARD_BUTTON || this.SHOW_DISCARD_BUTTON)
    && this.status === 'ACTIVE' && (!this.postingStatus || this.postingStatus==="DRAFT") ;
  }

  showVoid(): boolean {
    if(this.genDocLock){
      return false;
    }

    return (!this.appletSettings.HIDE_GENDOC_VOID_BUTTON || this.SHOW_VOID_BUTTON)
    && this.postingStatus === 'FINAL' && !this.eInvoiceEnabled
  }

  showClone() {
    return !this.HIDE_CLONE_BUTTON;
  }

  clone() {
    this.store.dispatch(SalesInvoiceActions.cloneDocumentInit({}));
  }

  onDiscard() {
    this.akaunDiscardDialogComponent = this.dialogRef.open(AkaunDiscardDialogComponent, { width: '400px' });
    this.akaunDiscardDialogComponent.componentInstance.confirmMessage = 'Are you sure you want to discard document ';
    this.akaunDiscardDialogComponent.componentInstance.docNo = this.docNo;
    this.akaunDiscardDialogComponent.afterClosed().subscribe((result) => {
      if(result === true) {
        this.store.dispatch(SalesInvoiceActions.discardInit({ guids: [this.hdrGuid.toString()], fromEdit: true }));
      }
    });
  }

  // ------------------- Expansion Panel  (Vertical UI) ------------------------ //

  initializeExpandedPanels(): void {
    // Initialize the expanded panels based on appletSettings
    const expandedIndex = this.panels.findIndex(panel =>
      panel.expandSetting && this.appletSettings[panel.expandSetting]
    );

    if (expandedIndex !== -1) {
      this.expandedPanelIndex = expandedIndex;
    }
  }

  onPanelOpened(index: number): void {
    if (this.orientation) {
      this.expandedPanelIndex = index;
      this.isPanelExpanded = true;
    }
  }

  savePanelState(): void {
    this.viewColFacade.updateInstance<LocalState>(this.index, {
      ...this.localState,
      expandedPanel: this.expandedPanelIndex
    });
  }

  getFilteredPanels(draft: any) {
    return this.panels.filter(panel => {
      const hidePanels = panel.hide && this.appletSettings[panel.hide];
      const conditionPanels = !panel.condition || panel.condition.status === draft?.status || this.appletSettings[panel?.show];
      return !hidePanels && conditionPanels;
    });
  }

  showPanels(): boolean {
    if(this.appletSettings?.VERTICAL_ORIENTATION){
      if(this.appletSettings?.DEFAULT_ORIENTATION === 'HORIZONTAL'){
        this.orientation = false;
      } else {
        this.orientation = true;
      }
    } else {
      if(this.appletSettings?.DEFAULT_ORIENTATION === 'VERTICAL'){
        this.orientation = true;
      } else {
        this.orientation = false;
      }
    }
    return this.orientation;
  }

  // --------------------------------------------------------------------------- //

  onFormStatusChange(status: string) {
    if (status === 'INVALID') {
      this.highlightMainTab(true);
    }else{
      this.highlightMainTab(false);
    }
  }

  highlightMainTab(boolean:boolean) {
    this.mainTabInvalid = boolean;
    const panelIndex = this.panels.findIndex(panel => panel.title === 'Main Details');
    this.panels[panelIndex].invalid = boolean;
  }

  onAccountStatusChange(status: string) {
    if (status === 'INVALID') {
      this.highlightAccountTab(true);
    }else{
      this.highlightAccountTab(false);
    }
  }

  highlightAccountTab(boolean:boolean) {
    this.accountTabInvalid = boolean;
    const panelIndex = this.panels.findIndex(panel => panel.title === 'Account');
    this.panels[panelIndex].invalid = boolean;
  }

  highlightLineTab(boolean:boolean) {
    this.lineTabInvalid = boolean;
    const linesPanelIndex = this.panels.findIndex(panel => panel.title === 'Lines');
    this.panels[linesPanelIndex].invalid = boolean;
  }

  shouldShowPanelTitle(panel: any){
    if (panel.title === 'KO For' && this.appletSettings.HIDE_KO_FOR_TAB) {
      return false;
    }
    if (panel.title === 'TraceDocument' && this.appletSettings.HIDE_TRACE_DOCUMENT_TAB) {
      return false;
    }

    return true;
  }

  ngOnDestroy() {
    if (this.matTab) {
      if(this.appletSettings?.HIDE_DELIVERY_DETAILS){
        this.viewColFacade.updateInstance<LocalState>(this.index, {
          ...this.localState,
          selectedIndex: this.matTab?.selectedIndex,
          accountSelectedIndex: this.account?.matTab?.selectedIndex,
          selectedSearchIndex: this.search?.matTab?.selectedIndex
        });
      }else{
        this.viewColFacade.updateInstance<LocalState>(this.index, {
          ...this.localState,
          selectedIndex: this.matTab?.selectedIndex,
          accountSelectedIndex: this.account?.matTab?.selectedIndex,
          deliveryDetailSelectedIndex: this.deliveryDetails?.matTab?.selectedIndex,
          selectedSearchIndex: this.search?.matTab?.selectedIndex
        });
      }
    }

    if (this.orientation) {
      this.savePanelState();
      this.store.dispatch(SalesInvoiceActions.resetExpansionPanel({ resetIndex: null }));
    }
    this.subs.unsubscribe();
  }

  disableResetButton() {
    return this.postingStatus === "FINAL" || this.status !== "ACTIVE" || this.status !== "TEMP";
  }

  isNotShadow() {
    return !(this.hdr.bl_fi_generic_doc_hdr?.forex_doc_hdr_guid);
  }

  validateInput(): boolean {
    if (!this.main.validateSalesAgent()) {
      this.toastr.error(
        "Please select a Sales Agent to proceed.",
        "Sales Agent Required",
        {
          tapToDismiss: true,
          progressBar: true,
          timeOut: 0,
          extendedTimeOut: 0,
        }
      );
      return false;
    } else {
      return true;
    }
  }

}
