<div style="text-align: right; margin-top: 16px">
    <button mat-raised-button color="primary" type="button" (click)="onSave()">
      SAVE
    </button>
</div>


<mat-card>
  <mat-tab-group mat-stretch-tabs [dynamicHeight]="true">
    <mat-tab label="Sidebar Menu">
      <ul>
        <li *ngFor="let setting of settingsMain">
          <mat-slide-toggle [formControl]="form.controls[setting.controlName]">{{ setting.controlName }}</mat-slide-toggle>
        </li>
      </ul>
    </mat-tab>
 
    <mat-tab label="Stock Availability Listing">
      <ul>
        <li *ngFor="let setting of settings">
          <mat-slide-toggle [formControl]="form.controls[setting.controlName]">{{ setting.label }}</mat-slide-toggle>
        </li>
      </ul>
      <div fxLayout="column" class="p-10">
        <app-select-multi-pricing-scheme-drop-down [apiVisa]="apiVisa" [pricingScheme]="form.controls['PRICING_SCHEMES']"
          [label]="'Pricing Schemes'" [options]="{multiple: true}"></app-select-multi-pricing-scheme-drop-down>
      
        <app-select-multi-drop-down [apiVisa]="apiVisa" [form]="form.controls['PRICE_METRICS']"
          [defaultRows]="priceMetrics" [label]="'Price Metrics'" [options]="{multiple: false}"></app-select-multi-drop-down>
      </div>  
    </mat-tab>
    <mat-tab label="Stock Availability Details">
      <ul>
        <li *ngFor="let setting of settings2">
          <mat-slide-toggle [checked]="true" [formControl]="form.controls[setting.controlName]">{{ setting.label }}</mat-slide-toggle>
        </li>
      </ul>
    </mat-tab>
    <mat-tab label="Item Category Group">
      <div fxLayout="column" class="p-10">
        <ng-container *ngFor="let i of categoryArray">
          <app-select-multi-label-list-drop-down [apiVisa]="apiVisa" [labelList]="form.controls['ITEM_CATEGORY_GROUP_' + i]"
          [label]="'Item Category Group '+ i" [options]="'{}'"></app-select-multi-label-list-drop-down>
        </ng-container>
      </div>
    </mat-tab>
  </mat-tab-group>
</mat-card>
