import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { StoreModule } from "@ngrx/store";
import { EffectsModule } from "@ngrx/effects";

import { DropDownModule } from "blg-akaun-ng-lib";
import { AgGridModule } from "ag-grid-angular";
import { MatTabsModule } from '@angular/material/tabs'; // Ensure Angular Material Tabs module is imported

import { SlideRendererComponent } from "../utilities/slide-renderer/slide-renderer.component";
import { UtilitiesModule } from "projects/shared-utilities/utilities/utilities.module";
import { NgxMatIntlTelInputModule } from 'ngx-mat-intl-tel-input';
import { SalesInvoiceContainerComponent } from "./sales-invoice-container.component";
import { SalesInvoiceListingComponent } from "./sales-invoice-listing/sales-invoice-listing.component";
import { SalesInvoiceCreateComponent } from "./sales-invoice-create/sales-invoice-create.component";
import { SalesInvoiceMainComponent } from "./sales-invoice-create/main-details/main-details.component";
import { SalesInvoiceAccountComponent } from "./sales-invoice-create/account/account.component";
import { AccountEntityDetailsComponent } from "./sales-invoice-create/account/account-entity-details/account-entity-details.component";
import { AccountBillingAddressComponent } from "./sales-invoice-create/account/account-billing-address/account-billing-address.component";
import { AccountShippingAddressComponent } from "./sales-invoice-create/account/account-shipping-address/account-shipping-address.component";
import { LineItemListingComponent } from "./sales-invoice-create/line-item/line-item-listing.component";
import { LineItemCreateComponent } from "./sales-invoice-create/line-item/line-item-create/line-item-create.component";
import { LineSearchItemListingComponent } from "./sales-invoice-create/line-item/line-item-create/search-item/line-search-item-listing.component";
import { LineQuotationItemListingComponent } from "./sales-invoice-create/line-item/line-item-create/quotation-item/line-quotation-item-listing.component";
import { LineSupplierDeliveryOrderItemListingComponent } from "./sales-invoice-create/line-item/line-item-create/supplier-delivery-order-item/line-supplier-delivery-order-item-listing.component";
import { LineJobsheetItemListingComponent } from "./sales-invoice-create/line-item/line-item-create/jobsheet-item/line-jobsheet-item-listing.component";
import { LineSalesOrderItemListingComponent } from "./sales-invoice-create/line-item/line-item-create/sales-order-item/line-sales-order-item-listing.component";
import { LineItemDetailsComponent } from "./sales-invoice-create/add-line-item/item-details/item-details.component";
import { ItemDetailsMainComponent } from "./sales-invoice-create/add-line-item/item-details/main-details/main-details.component";
import { ItemDetailsDeliveryInstructions } from "./sales-invoice-create/add-line-item/item-details/delivery-instructions/delivery-instructions.component";
import { AddLineItemComponent } from "./sales-invoice-create/add-line-item/add-line-item.component";
import { ItemDetailsDepartmentComponent } from "./sales-invoice-create/add-line-item/item-details/department/department.component";
import { ItemDetailsRelatedDocumentsComponent } from "./sales-invoice-create/add-line-item/item-details/related-documents/related-documents.component";
import { RelatedDocumentsCopyFromComponent } from "./sales-invoice-create/add-line-item/item-details/related-documents/copy-from/copy-from.component";
import { RelatedDocumentsCopyToComponent } from "./sales-invoice-create/add-line-item/item-details/related-documents/copy-to/copy-to.component";
import { ItemDetailsDeliveryTripsComponent } from "./sales-invoice-create/add-line-item/item-details/delivery-trips/delivery-trips.component";
import { LineItemSerialNumberComponent } from "./sales-invoice-create/add-line-item/serial-number/serial-number.component";
import { LineItemBatchNumberComponent } from "./sales-invoice-create/add-line-item/batch-number/batch-number.component";
import { LineItemBatchNumberListingComponent } from "./sales-invoice-create/add-line-item/batch-number/batch-number-listing/batch-number-listing.component";
import { LineItemBinNumberComponent } from "./sales-invoice-create/add-line-item/bin-number/bin-number.component";
import { LineItemBinNumberListingComponent } from "./sales-invoice-create/add-line-item/bin-number/bin-number-listing/bin-number-listing.component";
import { SerialNumberScanComponent } from "./sales-invoice-create/add-line-item/serial-number/serial-number-scan/serial-number-scan.component";
import { SerialNumberImportComponent } from "./sales-invoice-create/add-line-item/serial-number/serial-number-import/serial-number-import.component";
import { SerialNumberListingComponent } from "./sales-invoice-create/add-line-item/serial-number/serial-number-listing/serial-number-listing.component";
import { LineItemCostingDetailsComponent } from "./sales-invoice-create/add-line-item/costing-details/costing-details.component";
import { LineItemPricingDetailsComponent } from "./sales-invoice-create/add-line-item/pricing-details/pricing-details.component";
import { LineItemIssueLinkListingComponent } from "./sales-invoice-create/add-line-item/issue-link/issue-link-listing/issue-link-listing.component";
import { LineItemIssueLinkEditComponent } from "./sales-invoice-create/add-line-item/issue-link/issue-link-edit/issue-link-edit.component";
import { IssueLinkEditDetailsComponent } from "./sales-invoice-create/add-line-item/issue-link/issue-link-edit/details/issue-link-edit-details.component";
import { IssueLinkEditPlanningComponent } from "./sales-invoice-create/add-line-item/issue-link/issue-link-edit/planning/issue-link-edit-planning.component";
import { IssueLinkEditAttachmentComponent } from "./sales-invoice-create/add-line-item/issue-link/issue-link-edit/attachment/issue-link-edit-attachment.component";
import { IssueLinkEditCommentComponent } from "./sales-invoice-create/add-line-item/issue-link/issue-link-edit/comment/issue-link-edit-comment.component";
import { IssueLinkEditSubtasksComponent } from "./sales-invoice-create/add-line-item/issue-link/issue-link-edit/subtasks/issue-link-edit-subtasks.component";
import { IssueLinkEditLinkedIssuesComponent } from "./sales-invoice-create/add-line-item/issue-link/issue-link-edit/linked-issues/issue-link-edit-linked-issues.component";
import { IssueLinkEditWorklogComponent } from "./sales-invoice-create/add-line-item/issue-link/issue-link-edit/worklog/issue-link-edit-worklog.component";
import { IssueLinkEditActivityComponent } from "./sales-invoice-create/add-line-item/issue-link/issue-link-edit/activity/issue-link-edit-activity.component";
import { IssueLinkEditLogTimeComponent } from "./sales-invoice-create/add-line-item/issue-link/issue-link-edit/worklog/log-time/log-time.component";
import { DeliveryTripsComponent } from "./sales-invoice-create/delivery-trips/delivery-trips.component";
import { PaymentListingComponent } from "./sales-invoice-create/payment/payment-listing.component";
import { DepartmentComponent } from "./sales-invoice-create/department/department.component";
import { ContraListingComponent } from "./sales-invoice-create/contra/contra-listing.component";
import { ContraSelectDocumentComponent } from "./sales-invoice-create/contra/select-document/contra-select-document.component";
import { AddContraComponent } from "./sales-invoice-create/contra/add-contra/add-contra.component";
import { EditContraComponent } from "./sales-invoice-edit/edit-contra/edit-contra.component";
import { SelectBillingAddressComponent } from "./sales-invoice-create/account/account-billing-address/select-billing-address/select-billing-address-listing.component";
import { SelectShippingAddressComponent } from "./sales-invoice-create/account/account-shipping-address/select-shipping-address/select-shipping-address-listing.component";
import { MainSelectSalesAgentListingComponent } from "./sales-invoice-create/main-details/select-sales-agent/select-sales-agent-listing.component";
import { AddPaymentComponent } from "./sales-invoice-create/payment/add-payment/add-payment.component";
import { EditPaymentComponent } from "./sales-invoice-edit/edit-payment/edit-payment.component";
import { SalesInvoiceEditComponent } from "./sales-invoice-edit/sales-invoice-edit.component";
import { EditLineItemComponent } from "./sales-invoice-edit/edit-line-item/edit-line-item.component";
import { SalesInvoiceExportComponent } from "./sales-invoice-create/export/export.component";
import { reducers } from "../../state-controllers/sales-invoice-controller/store/reducers";
import { reducerss } from "../../state-controllers/customer-controller/reducers";
import { SalesInvoiceEffects } from "../../state-controllers/sales-invoice-controller/store/effects";
import { ItemEffects } from "../../state-controllers/sales-invoice-controller/store/effects";
import { SalesInvoiceFeatureKey } from "../../state-controllers/sales-invoice-controller/store/reducers";
import { AdvancedSearchModule } from "../utilities/advanced-search/advanced-search.module";
import { AppletUtilitiesModule } from "../../applet-utilities.module";
import { AddAttachmentsComponent } from "./sales-invoice-edit/attachments/add-attachments/add-attachments.component";
import { AttachmentsListingComponent } from "./sales-invoice-edit/attachments/attachments-listing.component";
import { HeaderDocLinkComponent } from "./sales-invoice-edit/header-doc-link/header-doc-link.component";
import { HeaderDocLinkCopyFromComponent } from "./sales-invoice-edit/header-doc-link/copy-from/copy-from.component";
import { HeaderDocLinkCopyToComponent } from "./sales-invoice-edit/header-doc-link/copy-to/copy-to.component";
import { MainSelectMemberListingComponent } from "./sales-invoice-create/main-details/select-member/select-member-listing.component";
import { PostingComponent } from "./sales-invoice-create/posting/posting.component";
import { SalesInvoiceImportComponent } from "./sales-invoice-create/import/import.component";
import { KoForSalesOrderItemComponent } from "./sales-invoice-create/line-item/line-item-create/ko-for-sales-order-item/ko-for-sales-order-item.component";
import { KoBySalesOrderItemComponent } from "./sales-invoice-create/line-item/line-item-create/ko-by-sales-order-item/ko-by-sales-order-item.component";
import { KoByJobsheetItemComponent } from "./sales-invoice-create/line-item/line-item-create/ko-by-jobsheet-item/ko-by-jobsheet-item.component";
import { KoForJobsheetItemComponent } from "./sales-invoice-create/line-item/line-item-create/ko-for-jobsheet-item/ko-for-jobsheet-item.component";
import { KoForQuotationItemComponent } from "./sales-invoice-create/line-item/line-item-create/ko-for-quotation-item/ko-for-quotation-item.component";
import { KoByQuotationItemComponent } from "./sales-invoice-create/line-item/line-item-create/ko-by-quotation-item/ko-by-quotation-item.component";
import { DeliveryDetailsComponent } from "./sales-invoice-create/delivery-details/delivery-details.component";
import { ItemDetailsDeliveryDetailsComponent } from "./sales-invoice-create/add-line-item/item-details/delivery-details/delivery-details.component";
import { CustomerEditComponent } from "./sales-invoice-create/account/account-entity-details/select-customer/customer-edit/customer-edit.component";
import { CustomerBranchComponent } from "./sales-invoice-create/account/account-entity-details/select-customer/customer-edit/customer-branch/customer-branch.component";
import { CustomerCreateMainComponent } from "./sales-invoice-create/account/account-entity-details/select-customer/customer-create/customer-create-main/customer-create-main.component";
import { CustomerCreateComponent } from "./sales-invoice-create/account/account-entity-details/select-customer/customer-create/customer-create.component";
import { CreateAddressComponent } from "./sales-invoice-create/account/account-entity-details/select-customer/customer-edit/customer-address/address-create/customer-address-create.component";
import { EditAddressComponent } from "./sales-invoice-create/account/account-entity-details/select-customer/customer-edit/customer-address/address-edit/customer-address-edit.component";
import { CustomerAddressComponent } from "./sales-invoice-create/account/account-entity-details/select-customer/customer-edit/customer-address/customer-address.component";
import { CreateBranchComponent } from "./sales-invoice-create/account/account-entity-details/select-customer/customer-edit/customer-branch/branch-create/branch-create.component";
import { EditBranchComponent } from "./sales-invoice-create/account/account-entity-details/select-customer/customer-edit/customer-branch/branch-edit/branch-edit.component";
import { AddCategoryComponent } from "./sales-invoice-create/account/account-entity-details/select-customer/customer-edit/customer-category/category-add/category-add.component";
import { EditCategoryComponent } from "./sales-invoice-create/account/account-entity-details/select-customer/customer-edit/customer-category/category-edit/category-edit.component";
import { CustomerCategoryComponent } from "./sales-invoice-create/account/account-entity-details/select-customer/customer-edit/customer-category/customer-category.component";
import { CustomerCompanyListingComponent } from "./sales-invoice-create/account/account-entity-details/select-customer/customer-edit/customer-company/supplier-company-listing/customer-company-listing.component";
import { CreateContactComponent } from "./sales-invoice-create/account/account-entity-details/select-customer/customer-edit/customer-contact/contact-create/customer-contact-create.component";
import { EditContactComponent } from "./sales-invoice-create/account/account-entity-details/select-customer/customer-edit/customer-contact/contact-edit/customer-contact-edit.component";
import { CustomerContactComponent } from "./sales-invoice-create/account/account-entity-details/select-customer/customer-edit/customer-contact/customer-contact.component";
import { CustomerItemPricingComponent } from "./sales-invoice-create/account/account-entity-details/select-customer/customer-edit/customer-item-pricing/customer-item-pricing-listing.component";
import { EditItemPricingComponent } from "./sales-invoice-create/account/account-entity-details/select-customer/customer-edit/customer-item-pricing/item-pricing-edit/item-pricing-edit.component";
import { CustomerLoginComponent } from "./sales-invoice-create/account/account-entity-details/select-customer/customer-edit/customer-login/customer-login.component";
import { CreateLoginComponent } from "./sales-invoice-create/account/account-entity-details/select-customer/customer-edit/customer-login/login-create/login-create.component";
import { EditLoginComponent } from "./sales-invoice-create/account/account-entity-details/select-customer/customer-edit/customer-login/login-edit/login-edit.component";
import { CustomerPaymentConfigComponent } from "./sales-invoice-create/account/account-entity-details/select-customer/customer-edit/customer-payment-config/customer-payment-config.component";
import { CreatePaymentConfigComponent } from "./sales-invoice-create/account/account-entity-details/select-customer/customer-edit/customer-payment-config/payment-config-create/payment-config-create.component";
import { EditPaymentConfigComponent } from "./sales-invoice-create/account/account-entity-details/select-customer/customer-edit/customer-payment-config/payment-config-edit/payment-config-edit.component";
import { CustomerTaxComponent } from "./sales-invoice-create/account/account-entity-details/select-customer/customer-edit/customer-tax/customer-tax.component";
import { CreateTaxComponent } from "./sales-invoice-create/account/account-entity-details/select-customer/customer-edit/customer-tax/tax-create/customer-tax-create.component";
import { EditTaxComponent } from "./sales-invoice-create/account/account-entity-details/select-customer/customer-edit/customer-tax/tax-edit/customer-tax-edit.component";
import { CustomerCompanyCreateComponent } from "./sales-invoice-create/account/account-entity-details/select-customer/customer-edit/customer-company/supplier-company-listing/customer-company-create/customer-company-create.component";
import { CustomerCompanyEditComponent } from "./sales-invoice-create/account/account-entity-details/select-customer/customer-edit/customer-company/supplier-company-listing/customer-company-edit/customer-company-edit.component";
import { customerFeatureKey } from "../../state-controllers/customer-controller/reducers/customer.reducers";
import { CustomerEffects } from "../../state-controllers/customer-controller/effects";
import { SelectCustomerListingComponent } from "./sales-invoice-create/account/account-entity-details/select-customer/select-customer-listing.component";
import { FroalaEditorModule } from "angular-froala-wysiwyg";
import { ImportKnockOffComponent } from "./sales-invoice-create/import-knock-off/import-knock-off.component";
import { KnockOffSalesOrderComponent } from "./sales-invoice-create/import-knock-off/knock-off-sales-order/knock-off-sales-order.component";
import { KnockOffSalesQuoatationComponent } from "./sales-invoice-create/import-knock-off/knock-off-sales-quoatation/knock-off-sales-quoatation.component";
import { KnockOffJobsheetComponent } from "./sales-invoice-create/import-knock-off/knock-off-jobsheet/knock-off-jobsheet.component";
import { columnViewModelFeatureKey } from "../../state-controllers/sales-invoice-view-model-controller/reducers";
import { columnViewModelReducers } from "../../state-controllers/sales-invoice-view-model-controller/reducers";
import { Column4ViewModelEffects } from "../../state-controllers/sales-invoice-view-model-controller/effects/column_4_view_model.effects";
import { AdvancedSearchContraModule } from '../utilities/advanced-search-contra/advanced-search-contra.module';
import {
  ErrorStateMatcher,
  MAT_DATE_LOCALE,
  ShowOnDirtyErrorStateMatcher,
} from "@angular/material/core";
import { ExternalDeliveryComponent } from "./sales-invoice-create/delivery-details/external-delivery/external-delivery.component";
import { InternalDeliveryComponent } from "./sales-invoice-create/delivery-details/internal-delivery/internal-delivery.component";
import { PickupComponent } from "./sales-invoice-create/delivery-details/pickup/pickup.component";
import {
  NgxMatDateAdapter,
  NgxMatDateFormats,
  NgxMatDatetimePickerModule,
  NgxMatNativeDateModule,
  NGX_MAT_DATE_FORMATS,
} from "@angular-material-components/datetime-picker";
import { CustomNgxDatetimeAdapter } from "../../models/custom-ngx-date-time-adapter";
import { NGX_MAT_MOMENT_DATE_ADAPTER_OPTIONS } from "@angular-material-components/moment-adapter";
import { DateCellRendererComponent } from "../utilities/date-cell-renderer/date-cell-renderer.component";
import { KnockOffDeliveryOrderComponent } from './sales-invoice-create/import-knock-off/knock-off-delivery-order/knock-off-delivery-order.component';
import { SalesInvoiceARAPComponent } from "./sales-invoice-edit/arap/arap-details.component";
import { DeliveryRegionCellRendererComponent } from '../utilities/delivery-region-cell-renderer/delivery-region-cell-renderer.component';
import { ShippingBranchCellRendererComponent } from '../utilities/shipping-branch-cell-renderer/shipping-branch-cell-renderer.component';
import { ShippingLocationCellRendererComponent } from '../utilities/shipping-location-cell-renderer/shipping-location-cell-renderer.component';
import { DeliveryDetailsCreateComponent } from "./sales-invoice-create/delivery-details-create/delivery-details.component";
import { ButtonDeleteRendererComponent } from "../button-del-renderer/button-del-renderer.component";
import { DeliveryTypeCellRendererComponent } from "projects/shared-utilities/utilities/delivery-type-cell-renderer/delivery-type-cell-renderer.component";
import { RequireDeliveryCellRendererComponent } from "projects/shared-utilities/utilities/require-delivery-cell-renderer/require-delivery-cell-renderer.component";
import { SalesInvoiceReturnPopUpComponent } from './sales-invoice-create/return-popup/popup.component';
import { DeliverySlideRendererComponent } from "../utilities/delivery-slide-renderer/delivery-slide-renderer.component";
import { PaginationClientSideV3Component } from "projects/shared-utilities/utilities/pagination-client-side-v3/pagination-client-side-v3.component";
import { CustomFooterComponent } from "../utilities/custom-footer/custom-footer.component";
import { KnockOffSalesOrderDraftComponent } from "./sales-invoice-create/import-knock-off/knock-off-sales-order-draft/knock-off-sales-order-draft.component";
import { MultiLevelDiscountComponent } from "./sales-invoice-create/add-line-item/item-details/multi-level-discount/multi-level-discount.component";
import { SelectBranchIntercompanyConfigComponent } from "./sales-invoice-create/account/intercompany-transaction/select-branch-intercompany-config/select-branch-intercompany-config.component";
import { IntercompanyTransactionComponent } from "./sales-invoice-create/account/intercompany-transaction/intercompany-transaction.component";
import { EInvoiceComponent } from "./sales-invoice-edit/e-invoice/e-invoice.component";
import { PendingPostingToIRBOrBatchQueueComponent } from "./sales-invoice-edit/e-invoice/pending-posting-to-irb-or-batch-queue/pending-posting-to-irb-or-batch-queue.component";
import { PendingInBatchQueueComponent } from "./sales-invoice-edit/e-invoice/pending-in-batch-queue/pending-in-batch-queue.component";
import { PendingSubmissionToIRBComponent } from "./sales-invoice-edit/e-invoice/pending-submission-to-irb/pending-submission-to-irb.component";
import { SubmittedToIRBComponent } from "./sales-invoice-edit/e-invoice/submitted-to-irb/submitted-to-irb.component";
import { SubmissionComponent } from "./sales-invoice-edit/e-invoice/submission/submission.component";
import { ProgressComponent } from "./sales-invoice-edit/e-invoice/progress/progress.component";
import { CommunicationComponent } from "./sales-invoice-edit/e-invoice/communication/communication.component";
import { CancellationComponent } from "./sales-invoice-edit/e-invoice/cancellation/cancellation.component";
import { NgxMatSelectSearchModule } from 'ngx-mat-select-search';
import { SelectDeliveryEntityComponent } from './sales-invoice-create/account/account-entity-details/select-delivery-entity/select-delivery-entity.component';
import { DeliveryEntityComponent } from './sales-invoice-create/delivery-details/delivery-entity/delivery-entity.component';
import { SelectSegmentTreeComponent } from "./sales-invoice-create/department/select-segment-tree/select-segment-tree.component";
import { SelectProjectTreeComponent } from "./sales-invoice-create/department/select-project-tree/select-project-tree.component";
import { SelectProfitCenterTreeComponent } from "./sales-invoice-create/department/select-profit-center-tree/select-profit-center-tree.component";
import { SelectDimensionTreeComponent } from "./sales-invoice-create/department/select-dimension-tree/select-dimension-tree.component";
import { DocLinkViewComponent } from "./sales-invoice-edit/header-doc-link/doc-link-view/doc-link-view.component";
import { DocLinkViewMainComponent } from "./sales-invoice-edit/header-doc-link/doc-link-view/doc-link-view-main/doc-link-view-main.component";
import { DoclinkViewLineComponent } from "./sales-invoice-edit/header-doc-link/doc-link-view/doc-link-view-line/doc-link-view-line.component";
import { DocLinkLevel2Component } from './sales-invoice-edit/header-doc-link/doc-link-view/doc-link/doc-link-level2.component';
import { DocLinkLevel2FromComponent} from './sales-invoice-edit/header-doc-link/doc-link-view/doc-link/from/from.component';
import { DocLinkLevel2ToComponent } from './sales-invoice-edit/header-doc-link/doc-link-view/doc-link/to/to.component';
import { CreateEInvoiceAddressComponent } from "./sales-invoice-create/account/account-entity-details/select-customer/customer-create/e-invoice-details/e-invoice-address-create/e-invoice-customer-address-create.component";
import { EInvoiceDetailsComponent } from "./sales-invoice-create/account/account-entity-details/select-customer/customer-create/e-invoice-details/e-invoice-details.component";
import { SearchDeliveryOrderComponent } from './sales-invoice-create/search-documents/search-delivery-order/search-delivery-order.component';
import { DoLineListingComponent } from './sales-invoice-create/search-documents/search-delivery-order/do-line-listing/do-line-listing.component';
import { SerialNumberCellRendererComponent } from "../utilities/serial-number-cell-renderer/serial-number-cell-renderer.component";
import { SearchJobsheetComponent } from './sales-invoice-create/search-documents/search-jobsheet/search-jobsheet.component';
import { SearchSalesOrderComponent } from './sales-invoice-create/search-documents/search-sales-order/search-sales-order.component';
import { SearchSalesQuotationComponent } from './sales-invoice-create/search-documents/search-sales-quotation/search-sales-quotation.component';
import { JsLineListingComponent } from './sales-invoice-create/search-documents/search-jobsheet/js-line-listing/js-line-listing.component';
import { SoLineListingComponent } from './sales-invoice-create/search-documents/search-sales-order/so-line-listing/so-line-listing.component';
import { SqLineListingComponent } from './sales-invoice-create/search-documents/search-sales-quotation/sq-line-listing/sq-line-listing.component';
import { SearchDocumentsComponent } from './sales-invoice-create/search-documents/search-documents.component';
import { EinvoiceNotificationQueueListingComponent } from "./sales-invoice-edit/e-invoice/notification-queue/notification-queue-listing.component";
import { SearchSalesInvoiceComponent } from './sales-invoice-create/search-documents/search-sales-invoice/search-sales-invoice.component';
import { SelectVehicleListingComponent } from "./sales-invoice-create/account/select-vehicle/vehicle-listing/select-vehicle-listing.component";
import { VehicleCreateEditComponent } from "./sales-invoice-create/account/select-vehicle/vehicle-create-edit.component/vehicle-create-edit.component";
import { VehicleMainComponent } from "./sales-invoice-create/account/select-vehicle/vehicle-create-edit.component/main-details/main-details.component";
import { VehicleEffects } from '../../state-controllers/vehicle-controller/store/effects/vehicle.effects';
import { vehicleFeatureKey } from '../../state-controllers/vehicle-controller/store/selectors/vehicle.selector';
import { vehicleReducers } from '../../state-controllers/vehicle-controller/store/reducers';
import { SelectVehicleComponent } from './sales-invoice-create/account/select-vehicle/select-vehicle.component';
import { SelectCustomerAndVehicleComponent } from './sales-invoice-create/account/account-entity-details/select-customer-vehicle/select-customer-vehicle.component';
import { BinNoModule } from './sales-invoice-create/add-line-item/bin-no-listing/bin-no.module';
import { SelectBinNoComponent } from './sales-invoice-create/add-line-item/bin-no-listing/select-bin-no/select-bin-no.component';
import { ChildItemsComponent } from "./sales-invoice-create/add-line-item/item-details/child-items/child-items.component";
import { ChildItemSearchComponent } from "./sales-invoice-create/add-line-item/item-details/child-item-search/child-item-search.component";
import { ChildItemAddComponent } from "./sales-invoice-create/add-line-item/item-details/child-item-add/child-item-add.component";
import { LineItemSalesHistoryComponent } from "./sales-invoice-create/add-line-item/item-details/sales-history/sales-history.component";
import { SellBelowMinPriceComponent } from './sales-invoice-edit/edit-line-item/sell-below-min-price/sell-below-min-price.component';
import { SwapSerialComponent } from "./sales-invoice-edit/edit-line-item/swap-serial/swap-serial.component";
import { SelectShippingEntityComponent } from './sales-invoice-create/account/account-shipping-address/select-shipping-entity/select-shipping-entity.component';
import { ImportExportComponent } from './sales-invoice-edit/e-invoice/import-export/import-export.component';
import { GrossProfitListingComponent } from "./sales-invoice-edit/gross-profit-listing/gross-profit-listing.component";

const CUSTOM_DATE_FORMATS: NgxMatDateFormats = {
  parse: {
    dateInput: "l, LTS",
  },
  display: {
    dateInput: "YYYY-MM-DD HH:mm:ss",
    monthYearLabel: "MMM YYYY",
    dateA11yLabel: "LL",
    monthYearA11yLabel: "MMMM YYYY",
  },
};
@NgModule({
  declarations: [
    SalesInvoiceContainerComponent,
    SalesInvoiceListingComponent,
    SalesInvoiceCreateComponent,
    SalesInvoiceEditComponent,
    SalesInvoiceMainComponent,
    MainSelectSalesAgentListingComponent,
    SalesInvoiceAccountComponent,
    AccountEntityDetailsComponent,
    SelectCustomerListingComponent,
    AccountBillingAddressComponent,
    SelectBillingAddressComponent,
    AccountShippingAddressComponent,
    SelectShippingAddressComponent,
    LineItemListingComponent,
    LineItemCreateComponent,
    LineSearchItemListingComponent,
    LineQuotationItemListingComponent,
    LineSupplierDeliveryOrderItemListingComponent,
    LineJobsheetItemListingComponent,
    LineSalesOrderItemListingComponent,
    AddLineItemComponent,
    EditLineItemComponent,
    LineItemDetailsComponent,
    ItemDetailsMainComponent,
    ItemDetailsDeliveryInstructions,
    ItemDetailsDepartmentComponent,
    ItemDetailsRelatedDocumentsComponent,
    RelatedDocumentsCopyFromComponent,
    RelatedDocumentsCopyToComponent,
    ItemDetailsDeliveryTripsComponent,
    LineItemSerialNumberComponent,
    LineItemBatchNumberComponent,
    LineItemBatchNumberListingComponent,
    LineItemBinNumberComponent,
    LineItemBinNumberListingComponent,
    SerialNumberScanComponent,
    SerialNumberImportComponent,
    SerialNumberListingComponent,
    LineItemCostingDetailsComponent,
    LineItemPricingDetailsComponent,
    LineItemIssueLinkListingComponent,
    LineItemIssueLinkEditComponent,
    IssueLinkEditDetailsComponent,
    IssueLinkEditPlanningComponent,
    IssueLinkEditAttachmentComponent,
    IssueLinkEditCommentComponent,
    IssueLinkEditSubtasksComponent,
    IssueLinkEditLinkedIssuesComponent,
    IssueLinkEditWorklogComponent,
    IssueLinkEditLogTimeComponent,
    IssueLinkEditActivityComponent,
    DeliveryTripsComponent,
    PaymentListingComponent,
    AddPaymentComponent,
    EditPaymentComponent,
    DepartmentComponent,
    ContraListingComponent,
    ContraSelectDocumentComponent,
    AddContraComponent,
    EditContraComponent,
    SalesInvoiceExportComponent,
    AttachmentsListingComponent,
    AddAttachmentsComponent,
    HeaderDocLinkComponent,
    HeaderDocLinkCopyFromComponent,
    HeaderDocLinkCopyToComponent,
    MainSelectMemberListingComponent,
    PostingComponent,
    SalesInvoiceImportComponent,
    KoForSalesOrderItemComponent,
    KoBySalesOrderItemComponent,
    KoByJobsheetItemComponent,
    KoForJobsheetItemComponent,
    KoForQuotationItemComponent,
    KoByQuotationItemComponent,
    DeliveryDetailsComponent,
    ItemDetailsDeliveryDetailsComponent,
    CustomerEditComponent,
    CustomerCreateComponent,
    CustomerCreateMainComponent,
    CustomerPaymentConfigComponent,
    EditPaymentConfigComponent,
    CreatePaymentConfigComponent,
    CustomerTaxComponent,
    CreateTaxComponent,
    EditTaxComponent,
    CreateAddressComponent,
    EditAddressComponent,
    CustomerAddressComponent,
    CreateContactComponent,
    EditContactComponent,
    CustomerContactComponent,
    CustomerLoginComponent,
    CreateLoginComponent,
    CustomerBranchComponent,
    EditLoginComponent,
    CreateBranchComponent,
    EditBranchComponent,
    CustomerCategoryComponent,
    EditCategoryComponent,
    AddCategoryComponent,
    CustomerItemPricingComponent,
    EditItemPricingComponent,
    CustomerCompanyListingComponent,
    CustomerCompanyEditComponent,
    CustomerCompanyCreateComponent,
    ImportKnockOffComponent,
    KnockOffSalesOrderComponent,
    KnockOffSalesQuoatationComponent,
    KnockOffJobsheetComponent,
    InternalDeliveryComponent,
    ExternalDeliveryComponent,
    PickupComponent,
    KnockOffDeliveryOrderComponent,
    SalesInvoiceARAPComponent,
    DateCellRendererComponent,
    DeliveryRegionCellRendererComponent,
    ShippingBranchCellRendererComponent,
    ShippingLocationCellRendererComponent,
    DeliveryDetailsCreateComponent,
    ButtonDeleteRendererComponent,
    SalesInvoiceReturnPopUpComponent,
    DeliverySlideRendererComponent,
    CustomFooterComponent,
    KnockOffSalesOrderDraftComponent,
    MultiLevelDiscountComponent,
    SelectBranchIntercompanyConfigComponent,
    IntercompanyTransactionComponent,
    EInvoiceComponent,
    PendingPostingToIRBOrBatchQueueComponent,
    PendingInBatchQueueComponent,
    PendingSubmissionToIRBComponent,
    SubmittedToIRBComponent,
    SubmissionComponent,
    CommunicationComponent,
    CancellationComponent,
    ProgressComponent,
    SelectDeliveryEntityComponent,
    DeliveryEntityComponent,
    SelectSegmentTreeComponent,
    SelectProjectTreeComponent,
    SelectProfitCenterTreeComponent,
    SelectDimensionTreeComponent,
    DocLinkViewComponent,
    DocLinkViewMainComponent,
    DoclinkViewLineComponent,
    DocLinkLevel2Component,
    DocLinkLevel2FromComponent,
    DocLinkLevel2ToComponent,
    EInvoiceDetailsComponent,
    CreateEInvoiceAddressComponent,
    SearchDeliveryOrderComponent,
    DoLineListingComponent,
    SerialNumberCellRendererComponent,
    SearchJobsheetComponent,
    SearchSalesOrderComponent,
    SearchSalesQuotationComponent,
    JsLineListingComponent,
    SoLineListingComponent,
    SqLineListingComponent,
    SearchDocumentsComponent,
    EinvoiceNotificationQueueListingComponent,
    SearchSalesInvoiceComponent,
    SelectVehicleListingComponent,
    VehicleCreateEditComponent,
    VehicleMainComponent,
    SelectVehicleComponent,
    SelectCustomerAndVehicleComponent,
    ChildItemsComponent,
    LineItemSalesHistoryComponent,
    ChildItemSearchComponent,
    ChildItemAddComponent,
    SelectBinNoComponent,
    SellBelowMinPriceComponent,
    SwapSerialComponent,
    SelectShippingEntityComponent,
    ImportExportComponent,
    GrossProfitListingComponent,
  ],
  imports: [
    CommonModule,
    UtilitiesModule,
    NgxMatIntlTelInputModule,
    DropDownModule,
    AdvancedSearchModule,
    AdvancedSearchContraModule,
    AppletUtilitiesModule,
    MatTabsModule,
    AgGridModule.withComponents([SlideRendererComponent, DateCellRendererComponent, DeliveryRegionCellRendererComponent, ShippingBranchCellRendererComponent, ShippingLocationCellRendererComponent, RequireDeliveryCellRendererComponent, DeliveryTypeCellRendererComponent, DeliverySlideRendererComponent, CustomFooterComponent, SerialNumberCellRendererComponent]),
    StoreModule.forFeature(SalesInvoiceFeatureKey, reducers),
    EffectsModule.forFeature([SalesInvoiceEffects, ItemEffects]),
    StoreModule.forFeature(customerFeatureKey, reducerss.customer),
    EffectsModule.forFeature([CustomerEffects]),
    StoreModule.forFeature(columnViewModelFeatureKey, columnViewModelReducers),
    EffectsModule.forFeature([Column4ViewModelEffects]),
    StoreModule.forFeature(vehicleFeatureKey, vehicleReducers.vehicle),
    EffectsModule.forFeature([VehicleEffects]),
    FroalaEditorModule.forRoot(),
    NgxMatDatetimePickerModule,
    NgxMatNativeDateModule,
    NgxMatSelectSearchModule,
    BinNoModule,
  ],
  providers: [
    {
      provide: NgxMatDateAdapter,
      useClass: CustomNgxDatetimeAdapter,
      deps: [MAT_DATE_LOCALE, NGX_MAT_MOMENT_DATE_ADAPTER_OPTIONS],
    },
    { provide: NGX_MAT_DATE_FORMATS, useValue: CUSTOM_DATE_FORMATS },
    PaginationClientSideV3Component,
  ],
})
export class SalesInvoiceModule {}
