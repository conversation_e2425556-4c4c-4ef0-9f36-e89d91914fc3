<mat-card>
    <mat-card-content>
        <h3>Version 1.32 (2025-07-17)</h3>
        <ul>
            <li>Refactored stock-availability-details-view-movement component to use ag-grid-custom</li>
            <li>Added automatic column visibility management based on settings and permissions using showColumns</li>
            <li>Add view-sales-report</li>
            <li>Fixed null check for this.data.locationRows in total-balance-cell-renderer component</li>
        </ul>
        <h3>Version 1.31 (2025-07-05)</h3>
        <ul>
            <li>Enhanced pricing scheme tooltip with dynamic column display based on permissions and settings</li>
            <li>Added conditional bold text styling for default price metric in pricing scheme tooltip</li>
            <li>Improved tooltip responsiveness with immediate hide delay when mouse leaves element</li>
        </ul>
        <h3>Version 1.30 (2025-06-07)</h3>
        <ul>
            <li>Resolved the issue where cell clicks were not working in the Stock Availability Details</li>
        </ul>
        <h3>Version 1.29 (2025-04-10)</h3>
        <ul>
            <li>Add stock Movement view</li>
            <li>Resolved issue that wrong qty of location at export</li>
        </ul>
        <h3>Version 1.28 (2025-03-07)</h3>
        <ul>
            <li>Enhanced item code search functionality in Serial Number Balance for improved accuracy and efficiency</li>
        </ul>
        <h3>Version 1.27 (2025-03-05)</h3>
        <ul>
            <li>Enhance stock availability details tracking by incorporating location labels</li>
        </ul>
        <h3>Version 1.26 (2025-01-14)</h3>
        <ul>
            <li>Allow to Save Advanced Search Filter Based on User Preferences</li>
        </ul>
        <h3>Version 1.25 (2025-01-10)</h3>
        <ul>
            <li>Add Total SO column of Stock Availability Details</li>
            <li>Add 1 Button to Collapse/Expand All for Pricing Scheme of Stock Availability Details</li>
        </ul>
        <h3>Version 1.24 (2024-12-31)</h3>
        <ul>
            <li>Add filter Item Code From and Item Code To Advanced Search</li>
        </ul>
        <h3>Version 1.23 (2024-12-03)</h3>
        <ul>
            <li>If no location selected, show warning message in red color</li>
        </ul>
        <h3>Version 1.22 (2024-11-15)</h3>
        <ul>
            <li>All Balance columns of Stock Availability Details have been updated from right-aligned to center-aligned</li>
        </ul>
        <h3>Version 1.21 (2024-11-13)</h3>
        <ul>
            <li>Stock that is 0 should be empty, branches with stock should have the amount coloured in green</li>
        </ul>
        <h3>Version 1.20 (2024-10-17)</h3>
        <ul>
            <li>Resolved Location Label filter at Stock Availability Details</li>
            <li>Resolved HIDE ZERO BALANCE filter not working at Stock Availability Details</li>
        </ul>
        <h3>Version 1.19 (2024-10-09)</h3>
        <ul>
            <li>Resolved Toggle bug at Stock Availability Details</li>
            <li>Show stock availability details when hide stock availability menu</li>
            <li>Add setting for default toggle column in setting and personalisation</li>
        </ul>
        <h3>Version 1.18 (2024-08-14)</h3>
        <ul>
            <li>Add status export bar</li>
            <li>Add a toggle icon to display the Stock Availability Details in full-page view</li>
            <li>Click the Doc Short Code in the Stock Movement section to expand and view the details</li>
        </ul>
        <h3>Version 1.17 (2024-07-19)</h3>
        <ul>
            <li>Remove pivot mode and Rows Group at side bar</li>
        </ul>
        <h3>Version 1.16 (2024-07-18)</h3>
        <ul>
            <li>Add Location Label filter</li>
        </ul>
        <h3>Version 1.15 (2024-07-17)</h3>
        <ul>
            <li>Add select Pricng Scheme Settings</li>
            <li>Add select Price Metrics Settings</li>
        </ul>
        <h3>Version 1.14 (2024-07-06)</h3>
        <ul>
            <li>Set Item Type Default Active</li>
            <li>Set Optional Default all check</li>
            <li>Change Size of Image</li>
            <li>Move Total Column before Location Columns</li>
            <li>Remove show tooltip at total row</li>
            <li>Hide Inv Item code and EAN Code</li>
        </ul>
        <h3>Version 1.13 (2024-04-12)</h3>
        <ul>
            <li>Add EAN code column of Stock Availability</li>
            <li>Add EAN code column of Stock Aging</li>
            <li>Add Optional for hide zero balance, hide goods received note balance, hide purchase order balance, and sales order balance</li>
            <li>Resolved that cannot display image of Stock Availability Details</li>
        </ul>
        <h3>Version 1.12 (2024-03-27)</h3>
        <ul>
            <li>Add Application Settings to Hide and Add Client Side Permissions</li>
            <li>Add EAN ref price2, ref price3, delta price2, delta price3, rebate price2, and rebate price3</li>
            <li>Add EAN code column</li>
            <li>Add GRN balance</li>
            <li>Add GRN tab of Stock Availability Details report</li>
        </ul>
        <h3>Version 1.11 (2024-03-06)</h3>
        <ul>
            <li>Add Stock Aging tab of Stock Availability Details report</li>
            <li>Add Stock Movement tab of Stock Availability Details report</li>
            <li>Add Purchase Order tab of Stock Availability Details report</li>
            <li>Add Sales Order tab of Stock Availability Details report</li>
        </ul>
        <h3>Version 1.10 (2024-02-07)</h3>
        <ul>
            <li>Add Serial number tab of Stock Availability Details report</li>
        </ul>
        <h3>Version 1.09 (2023-12-08)</h3>
        <ul>
            <li>Add pop up for 0 stock item</li>
            <li>Colour coding for inactive item</li>
        </ul>
        <h3>Version 1.08 (2023-11-23)</h3>
        <ul>
            <li>New page Stock Aging Report</li>
            <li>New page Trace Serial No</li>
        </ul>
        <h3>Version 1.07 (2023-11-18)</h3>
        <ul>
            <li>New page Stock Availability Details Listing</li>
        </ul>
        <h3>Version 1.06 (2023-08-14)</h3>
        <ul>
            <li>Add Total footer</li>
            <li>When stock balance is null, show 0</li>
        </ul>
        <h3>Version 1.05 (2023-08-09)</h3>
        <ul>
            <li>Add Sub Type column</li>
        </ul>
        <h3>Version 1.04 (2023-07-12)</h3>
        <ul>
            <li>Add category, item status and item type filter</li>
        </ul>
        <h3>Version 1.03 (2023-07-07)</h3>
        <ul>
            <li>Resolved that double stock balance</li>
        </ul>
        <h3>Version 1.02 (2023-03-10)</h3>
        <ul>
            <li>Change Listing to Pivot Mode</li>
            <li>Show Location Column</li>
            <li>Allow to click to view the details</li>
            <li>Show Item Category</li>
        </ul>
        <h3>Version 1.01 (2023-03-07)</h3>
        <ul>
            <li>Add Application Settings</li>
            <li>Hide Cost</li>
        </ul>
        <h3>Version 1.00 (2022-05-18)</h3>
    </mat-card-content>
  </mat-card>
