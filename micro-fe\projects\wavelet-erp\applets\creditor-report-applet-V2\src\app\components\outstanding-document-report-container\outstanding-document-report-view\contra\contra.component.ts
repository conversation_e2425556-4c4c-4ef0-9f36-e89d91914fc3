import { Component, Input, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { ARAPService, GenericDocARAPContainerModel, Pagination } from 'blg-akaun-ts-lib';
import { pageFiltering, pageSorting } from 'projects/shared-utilities/listing.utils';
import { AppConfig } from 'projects/shared-utilities/visa';
import { Observable } from 'rxjs';
import { SubSink } from 'subsink2';
import { ViewColumnFacade } from '../../../../facades/view-column.facade';

@Component({
  selector: 'app-contra',
  templateUrl: './contra.component.html',
  styleUrls: ['./contra.component.css']
})
export class ContraComponent implements OnInit, OnDestroy {

  @Input() localState: any;
  @Input() doc$: Observable<any>;

  private subs = new SubSink();

  gridApi;
  rowData = [];
  hdrGuid;
  SQLGuids: string[] = null;
  pagination = new Pagination();

  defaultColDef = {
    filter: 'agTextColumnFilter',
    // floatingFilter: true,
    floatingFilterComponentParams: {suppressFilterButton: true},
    minWidth: 200,
    flex: 2,
    sortable: true,
    resizable: true,
    suppressCsvExport: true
  };

  columnsDefs = [
    { headerName: 'Doc Type', field: 'bl_fi_generic_doc_arap_contra.server_doc_type_doc_2', cellStyle: () => ({ 'text-align': 'left' }) },
    { headerName: 'Status', field: 'bl_fi_generic_doc_arap_contra.status', cellStyle: () => ({ 'text-align': 'left' }) },
    { headerName: 'Description', field: 'bl_fi_generic_doc_arap_contra.server_doc_type_doc_2', cellStyle: () => ({ 'text-align': 'left' }) },
    { headerName: 'Amount Contra', field: 'bl_fi_generic_doc_arap_contra.amount_contra', type: 'numericColumn' }
  ];

  constructor(
    private viewColFacade: ViewColumnFacade,
    private arapContra: ARAPService
  ) { }
  protected readonly index = 1;
  ngOnInit(): void {
    this.subs.sink = this.doc$.subscribe({
      next: resolve => {
        this.hdrGuid = resolve.bl_fi_generic_doc_hdr.guid;
      }
    })
  }

  onGridReady(params) {
    this.gridApi = params.api;
    this.gridApi.closeToolPanel();
    this.setGridData();
  }

  setGridData() {
    const apiVisa = AppConfig.apiVisa;
    const datasource = {
      getRows: grid => {
        const filter = pageFiltering(grid.request.filterModel);
        const sortOn = pageSorting(grid.request.sortModel);
        this.pagination.offset = this.SQLGuids ? 0 : grid.request.startRow;
        this.pagination.limit = grid.request.endRow - grid.request.startRow;
        this.pagination.conditionalCriteria = [
          { columnName: 'calcTotalRecords', operator: '=', value: 'true' },
          { columnName: 'guid_doc_1_hdr', operator: '=', value: this.hdrGuid },
          { columnName: 'orderBy', operator: '=', value: 'updated_date' },
          { columnName: 'order', operator: '=', value: 'DESC' }
        ];

        this.subs.sink = this.arapContra.getByCriteria(this.pagination, apiVisa).subscribe(resolved => {
          const data = sortOn(resolved.data).filter(entity => filter.by(entity));
          const totalRecords = filter.isFiltering ? resolved.totalRecords : data.length;
          grid.success({
            rowData: data,
            rowCount: totalRecords
          });
        }, err => {
          console.log("contra failed");
          grid.fail();
        });
      }
    };
    this.gridApi.setServerSideDatasource(datasource);
  }

  canContra(){
    return true;
  }

  onContra() {
    this.viewColFacade.onNextAndReset(this.index, 4);
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }

}
