<mat-card-title class="column-title">
  <div fxLayout="row wrap" fxLayoutAlign="space-between end">
    <div>
      <button #navBtn mat-button class="blg-button-icon" matTooltip="Back" type="button"
        [disabled]="deactivateReturn$ | async" (click)="onReturn()">
        <img [ngClass]="navBtn.disabled ? 'blg-button-disabled' : null" src="../../../assets/images/backbutton.png" alt="add" width="40px" height="40px">
      </button>
      <span>
        Stock Availability Details View
      </span>
    </div>
  </div>
</mat-card-title>
<mat-tab-group [ngStyle]="(toggleColumn$ | async) ? {'max-width': '95vw'} : {'max-width': '43vw'}" [dynamicHeight]="true" [selectedIndex]="selectedIndex$ | async">
  <mat-tab label="Main">
    <app-stock-availability-details-view-main
      [entity$]="entity$"></app-stock-availability-details-view-main>
  </mat-tab>
  <mat-tab label="{{labelName()}}" *ngIf="isSerialNumber()">
    <app-stock-availability-view-serial-number
      [entity$]="entity$"></app-stock-availability-view-serial-number>
  </mat-tab>
  <mat-tab label="Bin Numbers" *ngIf="entity.sub_type == 'BIN_NUMBER'">
    <app-stock-avail-view-bins [locationGuid]="entity.location_guid" [invItemGuid]="entity.inv_item_guid"></app-stock-avail-view-bins>
  </mat-tab>
  <mat-tab label="Stock Movement" *ngIf="showStockMovement">
    <app-stock-availability-details-view-movement
      [entity$]="entity$"></app-stock-availability-details-view-movement>
  </mat-tab>
  <mat-tab label="Sales Report">
    <app-stock-availability-details-view-sales-report
      [entity$]="entity$"></app-stock-availability-details-view-sales-report>
  </mat-tab>
  <mat-tab label="Stock Aging">
    <app-stock-availability-details-view-aging
      [entity$]="entity$"></app-stock-availability-details-view-aging>
  </mat-tab>
  <mat-tab label="Goods Received Note">
    <app-stock-availability-details-view-grn
      [entity$]="entity$"></app-stock-availability-details-view-grn>
  </mat-tab> 
  <mat-tab label="Purchase Order">
    <app-stock-availability-details-view-po
      [entity$]="entity$"></app-stock-availability-details-view-po>
  </mat-tab>
  <mat-tab label="Sales Order">
    <app-stock-availability-details-view-so
      [entity$]="entity$"></app-stock-availability-details-view-so>
  </mat-tab>
  <mat-tab label="Images">
    <app-stock-availability-details-view-images
      [entity$]="entity$"></app-stock-availability-details-view-images>
  </mat-tab>
</mat-tab-group>
