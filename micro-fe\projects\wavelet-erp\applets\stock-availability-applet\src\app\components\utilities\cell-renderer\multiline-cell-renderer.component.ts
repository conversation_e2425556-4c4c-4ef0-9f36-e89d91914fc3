// multiline-cell-renderer.component.ts
import { Component, OnInit } from '@angular/core';
import { UtilitiesModule } from 'projects/shared-utilities/utilities/utilities.module';

@Component({
  selector: 'app-multiline-cell-renderer',
  template: `
    <div class="multiline-cell">
      <div>{{ firstRow }}</div>
      <div [ngClass]="{ 'positive-number': !isNegative(secondRow), 'negative-number': isNegative(secondRow)}">
        <font size=+1><b>{{ secondRow }}</b></font></div>
    </div>
  `,
  styles: [
    `
      .multiline-cell {
        display: flex;
        flex-direction: column;
      }

      .negative-number {
        color: red;
      }
      
      .positive-number {
        color: green;
      }      
    `,
  ],
})

// Refactor:  Consider extracting the logic for calculating and updating rows into separate, smaller functions to improve readability and maintainability.
// The aggregateData function could also be simplified.
export class MultilineCellRendererComponent implements OnInit {
  public data!: any;
  public firstRow: string;
  public secondRow: any;

  agInit(params: any): void {
    this.initializeRows();

    if (params.node && params.node.data) {
      this.data = params.node.data;
      this.calculateRows(this.data.locationRows[params.location_guid], {
        hideGRNBalance: this.data.hide_grn_balance,
        hidePOBalance: this.data.hide_po_balance,
        hideSOBalance: this.data.hide_so_balance,
      });
    } else {
      this.calculateFooterRows(params);
    }
  }

  private initializeRows(): void {
    this.firstRow = "";
    this.secondRow = "";
  }

  private calculateRows(locationRow: any, hideFlags: any): void {
    if (!locationRow) return;

    const { totalBalance, notes } = this.aggregateData(locationRow, hideFlags);
    this.updateRowValues(totalBalance, notes);

    if (this.secondRow === 0) {
      this.secondRow = "";
    }
  }

  private calculateFooterRows(params: any): void {
    const aggregatedData = { totalBalance: 0, notes: [] };

    params.api.forEachNode((node: any) => {
      if (node.data) {
        const locationRow = node.data.locationRows[params.location_guid];
        if (locationRow) {
          const { totalBalance, notes } = this.aggregateData(locationRow, {
            hideGRNBalance: node.data.hide_grn_balance,
            hidePOBalance: node.data.hide_po_balance,
            hideSOBalance: node.data.hide_so_balance,
          });

          aggregatedData.totalBalance += totalBalance;
          aggregatedData.notes.push(...notes);
        }
      }
    });

    this.updateRowValues(aggregatedData.totalBalance, aggregatedData.notes);
  }

  private aggregateData(locationRow: any, hideFlags: any): { totalBalance: number; notes: number[] } {
    let totalBalance = <number>locationRow.balance;
    const notes: number[] = [];

    if (!hideFlags.hideGRNBalance) notes.push(<number>locationRow.grn_qty);
    if (!hideFlags.hidePOBalance) notes.push(<number>locationRow.po_qty);
    if (!hideFlags.hideSOBalance) notes.push(-<number>locationRow.so_qty);

    return { totalBalance, notes };
  }

  private updateRowValues(totalBalance: number, notes: number[]): void {
    this.secondRow = totalBalance;

    if (notes.length > 0) {
      this.firstRow = '' + totalBalance;
      notes.forEach((n) => {
        this.firstRow += ` / ${Math.abs(n)}`;
        this.secondRow += n;
      });
    }
  }

  ngOnInit(): void {}

  isNegative(myNumber: number): boolean {
    return myNumber < 0;
  }
}
