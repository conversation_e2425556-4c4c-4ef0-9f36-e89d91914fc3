import { Component, OnInit } from '@angular/core';
import { GridOptions } from 'ag-grid-enterprise';

@Component({
  selector: 'app-gross-profit-listing',
  templateUrl: './gross-profit-listing.component.html',
  styleUrls: ['./gross-profit-listing.component.scss']
})
export class GrossProfitListingComponent implements OnInit {

  gridOptions: GridOptions = {
    defaultColDef: {
      sortable: true,
      resizable: true,
      filter: true,
      flex: 1,
      minWidth: 100
    },
    pagination: true,
    paginationPageSize: 20,
    rowSelection: 'single',
    animateRows: true
  };

  columnDefs = [
    {
      headerName: 'Item Code',
      field: 'itemCode',
      minWidth: 120,
      cellStyle: { 'text-align': 'left' }
    },
    {
      headerName: 'Item Name',
      field: 'itemName',
      minWidth: 200,
      cellStyle: { 'text-align': 'left' }
    },
    {
      headerName: 'Quantity',
      field: 'quantity',
      type: 'numericColumn',
      minWidth: 100,
      cellStyle: { 'text-align': 'right' }
    },
    {
      headerName: 'Unit Price',
      field: 'unitPrice',
      type: 'numericColumn',
      minWidth: 120,
      cellStyle: { 'text-align': 'right' },
      valueFormatter: params => this.currencyFormatter(params.value)
    },
    {
      headerName: 'Total Sales',
      field: 'totalSales',
      type: 'numericColumn',
      minWidth: 120,
      cellStyle: { 'text-align': 'right' },
      valueFormatter: params => this.currencyFormatter(params.value)
    },
    {
      headerName: 'Cost Price',
      field: 'costPrice',
      type: 'numericColumn',
      minWidth: 120,
      cellStyle: { 'text-align': 'right' },
      valueFormatter: params => this.currencyFormatter(params.value)
    },
    {
      headerName: 'Total Cost',
      field: 'totalCost',
      type: 'numericColumn',
      minWidth: 120,
      cellStyle: { 'text-align': 'right' },
      valueFormatter: params => this.currencyFormatter(params.value)
    },
    {
      headerName: 'Gross Profit',
      field: 'grossProfit',
      type: 'numericColumn',
      minWidth: 120,
      cellStyle: { 'text-align': 'right' },
      valueFormatter: params => this.currencyFormatter(params.value),
      cellClassRules: {
        'profit-positive': params => params.value > 0,
        'profit-negative': params => params.value < 0
      }
    },
    {
      headerName: 'Profit Margin (%)',
      field: 'profitMargin',
      type: 'numericColumn',
      minWidth: 130,
      cellStyle: { 'text-align': 'right' },
      valueFormatter: params => params.value ? `${params.value.toFixed(2)}%` : '0.00%',
      cellClassRules: {
        'profit-positive': params => params.value > 0,
        'profit-negative': params => params.value < 0
      }
    }
  ];

  rowData = [
    {
      itemCode: 'ITM001',
      itemName: 'Laptop Computer',
      quantity: 2,
      unitPrice: 1500.00,
      totalSales: 3000.00,
      costPrice: 1200.00,
      totalCost: 2400.00,
      grossProfit: 600.00,
      profitMargin: 20.00
    },
    {
      itemCode: 'ITM002',
      itemName: 'Wireless Mouse',
      quantity: 5,
      unitPrice: 25.00,
      totalSales: 125.00,
      costPrice: 15.00,
      totalCost: 75.00,
      grossProfit: 50.00,
      profitMargin: 40.00
    },
    {
      itemCode: 'ITM003',
      itemName: 'USB Cable',
      quantity: 10,
      unitPrice: 8.00,
      totalSales: 80.00,
      costPrice: 5.00,
      totalCost: 50.00,
      grossProfit: 30.00,
      profitMargin: 37.50
    },
    {
      itemCode: 'ITM004',
      itemName: 'Monitor Stand',
      quantity: 3,
      unitPrice: 45.00,
      totalSales: 135.00,
      costPrice: 35.00,
      totalCost: 105.00,
      grossProfit: 30.00,
      profitMargin: 22.22
    },
    {
      itemCode: 'ITM005',
      itemName: 'Keyboard',
      quantity: 4,
      unitPrice: 60.00,
      totalSales: 240.00,
      costPrice: 40.00,
      totalCost: 160.00,
      grossProfit: 80.00,
      profitMargin: 33.33
    }
  ];

  constructor() { }

  ngOnInit(): void {
    // Component initialization logic can be added here
  }

  currencyFormatter(value: number): string {
    if (value === null || value === undefined) {
      return '0.00';
    }
    return value.toLocaleString('en-US', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    });
  }

  onGridReady(params: any): void {
    params.api.sizeColumnsToFit();
  }

  onRowSelected(event: any): void {
    if (event.node.selected) {
      console.log('Selected row:', event.data);
    }
  }
}
