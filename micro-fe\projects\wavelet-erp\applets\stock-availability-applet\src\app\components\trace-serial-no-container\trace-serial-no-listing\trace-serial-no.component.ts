// Angular modules
import { ChangeDetectionStrategy, Component, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { FormControl, FormGroup } from '@angular/forms';

// NgRx
import { ComponentStore } from '@ngrx/component-store';
import { Store } from '@ngrx/store';

// 3rd-party libraries
import { ToastrService } from 'ngx-toastr';
import { SubSink } from 'subsink2';
import { GridOptions } from 'ag-grid-enterprise';
import { Observable, Subject, from } from 'rxjs';
import { debounceTime, distinctUntilChanged, mergeMap, map, toArray } from 'rxjs/operators';

// External libraries
import { TraceSerialNumberService, DocumentShortCodesClass } from 'blg-akaun-ts-lib';

// Shared libraries
import { AppConfig } from 'projects/shared-utilities/visa';
import { ViewColumnComponent } from 'projects/shared-utilities/view-column.component';

// Feature-specific imports
import { ViewColumnFacade } from '../../../facades/view-column.facade';

interface LocalState {
  deactivateAdd: boolean;
  deactivateList: boolean;
  selectedRowGuid: string;
}

@Component({
  selector: 'trace-serial-no-listing',
  templateUrl: './trace-serial-no.component.html',
  styleUrls: ['./trace-serial-no.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [ComponentStore]
})

export class TraceSerialNoListingComponent extends ViewColumnComponent implements OnInit, OnDestroy {

  protected subs = new SubSink();

  compId = "traceSerialNumberStockMovement"
  compName = 'Trace Serial Number Stock Movement';
  protected readonly index = 0;
  private localState: LocalState;

  readonly localState$ = this.viewColFacade.selectLocalState(this.index);
  readonly deactivateAdd$ = this.componentStore.select(state => state.deactivateAdd);
  readonly deactivateList$ = this.componentStore.select(state => state.deactivateList);

  showColumns = [
    { name: 'cost_ma_amount', setting: 'HIDE_LISTING_AVG_COST', permission: 'SHOW_LISTING_AVG_COST' }
  ];
  
  toggleColumn$: Observable<boolean>;
  public form: FormGroup;

  rowData = [];
  totalRecords = 0;
  limit = 50;

  // api visa
  apiVisa = AppConfig.apiVisa;

  // initial grid state
  gridApi;
  gridColumnApi;
  columnsDefs;

  checkAgGrid$: Observable<any>;
  dtoObject;

  gridOptions: GridOptions = {
    pagination: false,
    enableCharts: true,
    enableRangeSelection: true,
    rowSelection: 'multiple',
    suppressRowClickSelection: true,
    groupIncludeTotalFooter: true,
  };

  constructor(
    private viewColFacade: ViewColumnFacade,
    private readonly componentStore: ComponentStore<LocalState>,
    protected toastr: ToastrService,
    private traceService: TraceSerialNumberService,
  ) {
    super();
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }


  ngOnInit() {
    this.subs.sink = this.viewColFacade.rowDataTraceSerialNo$.subscribe(rowData => this.rowData = rowData);
    this.subs.sink = this.viewColFacade.totalRecordsTraceSerialNo$.subscribe(totalRecords => this.totalRecords = totalRecords);

    this.columnsDefs = [
      {headerName: 'Serial Number', field: 'reference_key_1', type: 'textColumn'},
      {headerName: 'Location', field: 'location_name', type: 'textColumn'},
      {headerName: 'Code', field: 'item_code', type: 'textColumn'},
      {headerName: 'Transaction Date', field: 'txn_date', type: 'dateColumn', sortable: true, sort: 'desc'},
      {headerName: 'Document', field: 'server_doc_1', type: 'textColumn'},
      {headerName: 'Server Doc Type', field: 'server_doc_type', type: 'textColumn',
        valueFormatter: (params) => params.value ? DocumentShortCodesClass.serverDocTypeToShortCodeMapper(params.value) : "",
      },
      {headerName: 'Customer/Supplier', field: 'name', type: 'textColumn'},
      {headerName: 'Reference', field: 'doc_reference', type: 'textColumn'},
      {headerName: 'Quantity', field: 'qty', type: 'integerColumn'},
      {headerName: 'Amount', field: 'amount_net', type: 'decimalColumn'},
      {headerName: 'MA Cost', field: 'cost_ma_amount', type: 'integerColumn'},
      {headerName: 'Posting Status', field: 'posting_status', type: 'textColumn'},
      {headerName: 'Remarks', field: 'formatted_txn_type', type: 'textColumn'}

    ];

    this.toggleColumn$ = this.viewColFacade.toggleColumn$;
    this.localState$.subscribe(a => {
      this.localState = a;
      this.componentStore.setState(a);
    });


    this.form = new FormGroup({
      docNo: new FormControl(),
      docNoKeyword: new FormControl(),
      from: new FormControl(),
      to: new FormControl(),
    });

    this.subs.sink = this.form.valueChanges.pipe(
      debounceTime(100),
      distinctUntilChanged()
    ).subscribe({
      next: (form) => {
        if(this.form.controls['docNo'].value){
          this.form.controls['docNoKeyword'].disable();
          this.form.controls['from'].disable();
          this.form.controls['to'].disable();
        }
        else if(this.form.controls['docNoKeyword'].value || this.form.controls['from'].value || this.form.controls['to'].value ){
          this.form.controls['docNo'].disable();
        }
        else{this.form.controls['docNo'].enable();
          this.form.controls['docNoKeyword'].enable();
          this.form.controls['from'].enable();
          this.form.controls['to'].enable();
        }
      }
    });
  }

  onGridReady(params) {
    this.gridApi = params.api;
    this.gridColumnApi = params.columnApi;
    this.gridApi.closeToolPanel();

    if (this.rowData.length > 0) {
      this.setDataRowCache();
    }
  }

  onSearch(){
    this.dtoObject = {
      'serialNumber' : this.form.controls['docNo'].value? this.form.controls['docNo'].value.toUpperCase(): null,
      'serialNumberKeyword' : this.form.controls['docNoKeyword'].value? this.form.controls['docNoKeyword'].value.toUpperCase(): null,
      'dateFrom' : this.form.controls['from'].value? new Date(this.form.controls['from'].value.format('YYYY-MM-DDT00:00:01')).toISOString(): null,
      'dateTo' : this.form.controls['to'].value? new Date(this.form.controls['to'].value.format('YYYY-MM-DDT23:59:59')).toISOString(): null,
      'includeDraft' : true,
    }

    this.createData();
  }

  createData() {
    //console.log("on create data....");
    this.clear();
    this.viewColFacade.loadInit(this.gridApi);
    this.subs.sink = this.traceService.post(this.dtoObject, this.apiVisa).pipe(
      mergeMap(a => from(a.data).pipe(
        map(b => {
          Object.assign(b, {
            formatted_txn_type: this.getFormattedTxnType(b['txn_type'])
          });
          return b;
        }),
        toArray(),
        map(c => {
          a.data = c;
          return a;
        })
      ))
    ).subscribe(resolved => {
      //console.log(resolved);
      this.viewColFacade.loadSuccess(resolved);
      this.rowData = resolved.data;
      this.totalRecords = this.rowData.length;
      //this.rowData = [...this.rowData, ...resolved.data];
      this.gridApi.setRowData(this.rowData);
      this.viewColFacade.selectRowDataTraceSerialNo(this.rowData);
      this.viewColFacade.selectTotalRecordsTraceSerialNo(this.totalRecords);
    }, err => {
      console.error(err);
      if(err.status===403)
        {
          this.viewColFacade.handle403Error(err,'GET')
        }
        else{
      this.toastr.error(
        err.message,
        "AG Gird Error",
        {
          tapToDismiss: true,
          progressBar: true,
          timeOut: 1300
        }
      );
    }
      this.viewColFacade.loadFailed(err);
    });

  };

  clear() {
    this.gridApi.setRowData([]);
    this.totalRecords = 0;
    this.rowData = [];
  }

  setDataRowCache() {
    this.gridApi.setRowData(this.rowData);
  }

  getRowStyle = params => {
    if (params.node.footer) {
      return { fontWeight: 'bold', background: '#e6f7ff' };
    }
    if (params.node.group) {
      return { fontWeight: 'bold' };
    }
  }

  getFormattedTxnType(txnType: string): string {
    if (txnType === 'SN_DATAFIX') {
      return "Serial Number Adjustment";
    } else if (txnType === 'SN_ADJ') {
      return "Stock Adjustment By Serial Item";
    } else {
      return "";
    }
  }

  onReset(){
    this.form.reset();
  }
}

