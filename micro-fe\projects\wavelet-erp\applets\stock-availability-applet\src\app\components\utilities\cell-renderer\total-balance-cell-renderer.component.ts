// multiline-cell-renderer.component.ts
import { Component, OnInit } from '@angular/core';
import { UtilitiesModule } from 'projects/shared-utilities/utilities/utilities.module';

@Component({
  selector: 'app-total-balance-cell-renderer',
  template: `
    <div class="multiline-cell">
      <div>{{ firstRow }}</div>
      <div><font size=+1 color='blue'><b>{{ secondRow }}</b></font></div>
    </div>
  `,
  styles: [
    `
      .multiline-cell {
        display: flex;
        flex-direction: column;
      }
    `,
  ],
})
export class TotalBalanceCellRendererComponent implements OnInit {
  public data!: any;
  public firstRow: any;
  public secondRow: any;

  agInit(params: any): void {
    this.initializeRows();

    if (params.node && params.node.data) {
      this.data = params.api!.getDisplayedRowAtIndex(params.rowIndex!)!.data;
      if (this.data) {
        this.calculateRows(this.data);
      }
    } else {
      this.calculateFooterRows(params);
    }
  }

  private initializeRows(): void {
    this.firstRow = "";
    this.secondRow = "0";
  }

  private calculateRows(data: any): void {
    const { totalBalance, notes } = this.aggregateData(data.locationRows, {
      hideGRNBalance: data.hide_grn_balance,
      hidePOBalance: data.hide_po_balance,
      hideSOBalance: data.hide_so_balance,
    });

    this.updateRows(totalBalance, notes);
  }

  private calculateFooterRows(params: any): void {
    const aggregatedData = { totalBalance: 0, notes: [] };

    params.api.forEachNode((node: any) => {
      if (node.data && node.data.locationRows) {
        const { totalBalance, notes } = this.aggregateData(node.data.locationRows, {
          hideGRNBalance: node.data.hide_grn_balance,
          hidePOBalance: node.data.hide_po_balance,
          hideSOBalance: node.data.hide_so_balance,
        });

        aggregatedData.totalBalance += totalBalance;
        aggregatedData.notes.push(...notes);
      }
    });

    this.updateRows(aggregatedData.totalBalance, aggregatedData.notes);
  }

  private aggregateData(locationRows: any, hideFlags: any): { totalBalance: number; notes: number[] } {
    let totalBalance = 0;
    const notes: number[] = [];

    Object.keys(locationRows).forEach((locationGuid) => {
      const locationRow = locationRows[locationGuid];
      totalBalance += <number>locationRow.balance;

      if (!hideFlags.hideGRNBalance) notes.push(<number>locationRow.grn_qty);
      if (!hideFlags.hidePOBalance) notes.push(<number>locationRow.po_qty);
      if (!hideFlags.hideSOBalance) notes.push(-<number>locationRow.so_qty);
    });

    return { totalBalance, notes };
  }

  private updateRows(totalBalance: number, notes: number[]): void {
    this.secondRow = totalBalance;

    if (notes.length > 0) {
      this.firstRow = totalBalance;
      notes.forEach((n) => {
        this.firstRow += ` / ${Math.abs(n)}`;
        this.secondRow += n;
      });
    }
  }

  ngOnInit(): void {}
}
